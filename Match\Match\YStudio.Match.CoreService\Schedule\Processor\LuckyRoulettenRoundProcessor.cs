﻿using FreeRedis;
using FreeScheduler;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;
using YStudio.Match.CarsRacing;
using YStudio.Match.Domain.Enums;
using YStudio.Match.SpinWin;

namespace YStudio.Match.CoreService
{

    [Component]
    public class LuckyRoulettenRoundProcessor : IScheduleTaskHandler
    {
        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly MatchService matchService;

        [Autowired]
        protected readonly OrderService orderService;

        [Autowired]
        protected readonly WebSocketSessionManager sessionManager;

        [Autowired]
        protected readonly ILogger<LuckyRoulettenRoundProcessor> logger;

        public async Task OnExecutingAsync(TaskInfo task)
        {
            using (await AsyncLocker.LuckyRoulettenRoundLocker.LockAsync())
            {
                try
                {
                    await ExecuteRoundAsync(task);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "LuckyRoulettenRoundProcessor OnExecutingAsync error");
                }
            }
        }

        private async Task ExecuteRoundAsync(TaskInfo task)
        {
            string[] arr = task.Body.Split("_");

            int matchId = int.Parse(arr[0]);
            string date = arr[1];
            int round = int.Parse(arr[2]);
            Guid roundId = Guid.Parse(arr[3]);

            var pendingRound = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.Id == roundId)
                .Where(r => r.Status == ERoundStatus.Pending)
                .FirstAsync();

            if (pendingRound == null)
            {
                logger.LogWarning("LuckyRoulettenRoundProcessor OnExecutingAsync: Round {RoundId} is not pending or does not exist.", roundId);
                return;
            }

            var now = DateTime.Now;

            // 从数据库读取指定比赛和轮次的订单项统计
            var stakes = await orderService.GetOrderItemsGroupByOptionAsync(matchId, roundId);

            //只开一个数字
            SpinWinHandler spinWinHandler = new SpinWinHandler();

            //读取配置的保留比例
            var (reserveRatio, minPayoutRate) = await GetReserveRatioAsync();

            //处理下注数据，计算结果 0.1m是扣留赔付 0.1需要从数据库中读取
            var numResult = spinWinHandler.Handle(stakes, reserveRatio, minPayoutRate);

            //记录结算日志
            await dbContext.MatchRoundLogs.AsInsertable(new MatchRoundLog
            {
                MatchId = matchId,
                RoundId = pendingRound.Id,
                Round = pendingRound.Round,
                EndTime = pendingRound.EndTime,
                LogMessage = spinWinHandler.LogMessage.ToString()
            }).ExecuteCommandAsync();

            //1.更新结果到Round表
            await dbContext.MatchRounds.AsUpdateable(new MatchRound() { Id = roundId, Result = JsonUtils.SerializeObject(numResult), Status = ERoundStatus.Completed }).UpdateColumns(m => new { m.Result, m.Status }).ExecuteCommandAsync();

            //2.获取最新期数数据
            var recents = await matchService.GetRecentRoundsAsync(matchId, 120);
            // 获取下一期数据
            var nextRound = await matchService.GetNextRoundAsync(matchId, now);

            //当执行时间距离开奖时间小于10秒时，进行广播
            if ((now - pendingRound.EndTime) < TimeSpan.FromSeconds(10))
            {
                //3.广播结果
                BraodcastResult(matchId, round, now, nextRound, recents, numResult);
            }
            //4.更新订单信息
            //本次结果影响到的OptionId
            var optionList = spinWinHandler.GetOptionsByNumber(numResult);

            using (var uow = dbContext.CreateUnitOfWork())
            {
                //需要更新所有中奖结果数据的status 为win 选项ID在 optionList;
                await dbContext.OrderItems.AsUpdateable()
                    .SetColumns(o => o.Status == EOrderState.Win)
                    .SetColumns(o => o.ActualPayout == o.MaxPayout)
                    .Where(o => o.RoundId == roundId)
                    .Where(o => optionList.Contains(o.OptionId))
                    .Where(o => o.Status == EOrderState.Submitted)
                    .ExecuteCommandAsync();

                //需要更新所有未中奖结果数据的status 为Lose 选项ID在 optionList;
                await dbContext.OrderItems.AsUpdateable()
                    .SetColumns(o => o.Status == EOrderState.Lose)
                    .SetColumns(o => o.ActualPayout == 0)
                    .Where(o => o.RoundId == roundId)
                    .Where(o => !optionList.Contains(o.OptionId))
                    .Where(o => o.Status == EOrderState.Submitted)
                    .ExecuteCommandAsync();
                // 更新订单状态
                await orderService.UpdateOrderStatusByRound(roundId);

                uow.Commit();
            }
        }


        /// <summary>
        /// 读取配置的保留比例
        /// </summary>
        /// <returns></returns>
        private async Task<(decimal, int)> GetReserveRatioAsync()
        {
            decimal reserveRatio = 0.05m;
            int minPayoutRate = 0;
            var config = await dbContext.Configs.AsQueryable().Where(c => c.ConfigKey == EConfigKey.LuckyRouletten).FirstAsync();
            if (config != null && !string.IsNullOrEmpty(config.ConfigValue))
            {
                try
                {
                    var setting = JsonConvert.DeserializeObject<Dictionary<string, decimal>>(config.ConfigValue);
                    if (setting != null)
                    {
                        reserveRatio = setting.TryGetValue("reserveRatio", out decimal ratio) ? ratio : reserveRatio;
                        minPayoutRate = (int)(setting.TryGetValue("minPayoutRate", out decimal rate) ? rate : minPayoutRate);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "LuckyRoulettenRoundProcessor OnExecutingAsync: Error parsing LuckyRouletten config.");
                }
            }

            return (reserveRatio, minPayoutRate);
        }

        private void BraodcastResult(int matchId, int round, DateTime now, MatchRound nextRound, IList<MatchRound> recents, object numResult)
        {
            var result = new LuckyRoulettenResult
            {
                Command = ECommand.SpinRoundResult,
                MatchId = matchId,
                CurrentRound = round,
                CurrentTime = DateTimeUtils.ToTimestampMilliseconds(now),
                NextRound = nextRound.Round,
                NextTime = DateTimeUtils.ToTimestampMilliseconds(nextRound.EndTime),
                CurrentNumber = numResult,
                RecentResult = recents.Select(item => new LuckyRoulettenRoundResult
                {
                    Round = item.Round,
                    RoundTime = DateTimeUtils.ToTimestampMilliseconds(item.EndTime),
                    Result = JsonConvert.DeserializeObject<SpinNumber>(item.Result),
                }).ToList()
            };
            // 发送结果到所有订阅的客户端
            sessionManager.BroadcastToGroup(matchId.ToString(), JsonUtils.SerializeObject(result));
        }
    }
}
