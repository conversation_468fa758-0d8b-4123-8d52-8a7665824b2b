﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<targets>
		<target xsi:type="File"
				name="logfile"
				fileName="logs/${shortdate}.txt"
				layout="${longdate}|${level:uppercase=true}|${logger}|${message} ${exception}"
				archiveAboveSize="2097152"
				archiveNumbering="Sequence"
				maxArchiveFiles="10"
				concurrentWrites="true"
				keepFileOpen="false" />
		<target xsi:type="Console" name="logconsole" />
	</targets>
	<rules>
		<!-- 过滤掉 Microsoft.AspNetCore.Hosting.Diagnostics 日志 -->
		<logger name="Microsoft.AspNetCore.*" minlevel="Trace" maxlevel="Fatal" writeTo="" final="true" />

		
		<!-- 只记录 Error 及以上级别，或手动打印日志（假设 logger 名称为 Manual） -->
		<logger name="Manual" minlevel="Info" writeTo="logfile,logconsole" final="true" />
		<logger name="*" minlevel="Info" writeTo="logfile,logconsole" final="true" />
	</rules>
</nlog>