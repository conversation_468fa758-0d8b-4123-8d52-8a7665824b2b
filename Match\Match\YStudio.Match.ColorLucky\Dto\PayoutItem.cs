﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.ColorLucky
{
    public class PayoutItem
    {
        /// <summary>
        /// 订单类型 
        /// 0:数字组合
        /// 1:颜色
        /// </summary>
        public int Type { get; set; } = 0;

        /// <summary>
        /// 订单颜色
        /// </summary>
        public EColor? Color { get; set; }

        /// <summary>
        /// 订单组合数字
        /// </summary>
        public List<int> Numbers { get; set; }

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal Stake { get; set; } = 0;
        /// <summary>
        /// 赔付金额 包含子集组合
        /// </summary>
        public decimal Payout { get; set; } = 0;


        public PayoutItem(List<int> numbers, decimal stake)
        {
            Numbers = numbers.OrderBy(x => x).ToList();
            Stake = stake;
            Type = 0;
        }
    }
}
