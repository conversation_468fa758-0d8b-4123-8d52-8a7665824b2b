using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.ColorLucky
{
    public class ColorLuckyHandler
    {

        /// <summary>
        /// 所有的数字和颜色
        /// </summary>
        public static IList<ColorNumber> Numbers { get; set; } = new List<ColorNumber>();

        /// <summary>
        /// 所有的赔率选项
        /// </summary>
        public static List<ColorOption> Options { get; set; } = new List<ColorOption>();

        /// <summary>
        /// 选项赔率映射表 8中N, 7中N, 6中N, 5中N, 4中N, 3中3 每个选项的赔率信息
        /// </summary>
        private static readonly ConcurrentDictionary<string, decimal> OptionMap = new();


        public StringBuilder LogMessage = new StringBuilder();


        private ConcurrentDictionary<string, bool> RuleConfig = new ConcurrentDictionary<string, bool>(new Dictionary<string, bool>
        {
            { "8_8", false },
            { "8_7", false },
            { "7_7", false },
            { "7_6", false },
            { "6_6", false },
            { "6_5", false },
            { "5_5", false },
            { "4_4", false },
            { "3_3", false },

        });

        static ColorLuckyHandler()
        {
            var colors = new List<EColor> { EColor.Blue, EColor.Orange, EColor.Purple, EColor.Green };

            for (int i = 1; i <= 80; i++)
            {
                EColor color = colors[(i - 1) % 4];
                Numbers.Add(new(i, color));
            }


            //8中N
            Options.Add(new(8, 8, 50000));
            Options.Add(new(8, 7, 800));
            Options.Add(new(8, 6, 50));
            Options.Add(new(8, 5, 7));
            Options.Add(new(8, 4, 2));
            Options.Add(new(8, 3, 1));

            //7中N
            Options.Add(new(7, 7, 6000));
            Options.Add(new(7, 6, 200));
            Options.Add(new(7, 5, 40));
            Options.Add(new(7, 4, 3));
            Options.Add(new(7, 3, 1));

            //6中N
            Options.Add(new(6, 6, 2250));
            Options.Add(new(6, 5, 150));
            Options.Add(new(6, 4, 3));
            Options.Add(new(6, 3, 1));

            //5中N
            Options.Add(new(5, 5, 275));
            Options.Add(new(5, 4, 10));
            Options.Add(new(5, 3, 8));

            //4中N
            Options.Add(new(4, 4, 175));
            Options.Add(new(4, 3, 10));

            //3中3
            Options.Add(new(3, 3, 70));

            Options.ForEach(x =>
            {
                OptionMap.TryAdd($"{x.NumberCount}_{x.WinCount}", x.Odds);
            });
        }

        /// <summary>
        /// 根据下单的数字
        /// </summary>
        /// <returns></returns>
        public List<int> Handle(List<OrderItem>? orders, Dictionary<string, bool>? ruleConfig)
        {
            if (ruleConfig != null)
            {
                RuleConfig = new ConcurrentDictionary<string, bool>(ruleConfig);
            }

            //orders = new List<OrderItem>();

            var totalStake = orders.Sum(o => o.Stake);
            LogMessage.AppendLine($"累计下单金额: {totalStake}");
            LogMessage.AppendLine($"数字组合下单金额: {orders.Where(o => o.Type == 0).Sum(o => o.Stake)}");
            LogMessage.AppendLine($"颜色下单金额: {orders.Where(o => o.Type == 1).Sum(o => o.Stake)}");

            RuleConfig.Keys.ToList().ForEach(k =>
            {
                LogMessage.AppendLine($"规则配置: {k} " + (RuleConfig[k] ? "开" : "不开"));
            });

            //Random _random = new Random();

            //for (int i = 0; i < 50000; i++)
            //{
            //    int length = _random.Next(3, 8); // 随机长度：3 ~ 8
            //    var numbers = ColorNumberUtils.GenerateUniqueRandomNumbers(length);
            //    orders.Add(new OrderItem(numbers, 1000));
            //}
            //orders.Add(new(new List<int> { 8, 2, 3, 4, 5, 6, 7, 1 }, 1000));
            //orders.Add(new(new List<int> { 9, 10, 11, 12, 13, 14, 15, 16 }, 1000));
            //orders.Add(new(new List<int> { 17, 18, 19, 20, 21, 22, 23, 24 }, 1000));
            //orders.Add(new(new List<int> { 25, 26, 27, 28, 29, 30, 31, 32 }, 1000));
            //orders.Add(new(new List<int> { 33, 34, 35, 36, 37, 38, 39, 40 }, 1000));
            //orders.Add(new(new List<int> { 41, 42, 43, 44, 45, 46, 47, 48 }, 1000));
            //orders.Add(new(new List<int> { 49, 50, 51, 52, 53, 54, 55, 56 }, 1000));
            //orders.Add(new(new List<int> { 57, 58, 59, 60, 61, 62, 63, 64 }, 1000));
            //orders.Add(new(new List<int> { 65, 66, 67, 68, 69, 70, 71, 72 }, 1000));
            //orders.Add(new(new List<int> { 73, 74, 75, 76, 77, 78, 79, 80 }, 1000));

            //orders.Add(new(new List<int> { 9, 10, 11, 12, 13, 14, 15, 16 }, 1000));

            //orders.Add(new(new List<int> { 9, 10, 11, 12, 13, 14, 15, 16 }, 1000));
            //orders.Add(new(new List<int> { 9, 10, 11, 12, 13, 14, 15, 16 }, 1000));

            //orders.Add(new(new List<int> { 2, 3, 4, 5, 6, 7, 8, 9 }, 1000));
            //orders.Add(new(new List<int> { 1, 2, 3, 4, 5, 6, 7 }, 1000));
            //orders.Add(new(new List<int> { 1, 2, 3, 4, 5, 6 }, 1000));
            //orders.Add(new(new List<int> { 1, 2, 3, 4, 5 }, 1000));
            //orders.Add(new(new List<int> { 1, 2, 3, 4 }, 1000));
            //orders.Add(new(new List<int> { 1, 2, 3 }, 1000));
            //orders.Add(new(new List<int> { 1, 2, 3 }, 1000));
            //orders.Add(new(new List<int> { 1, 2, 3 }, 1000));

            /*
            8中 8  百分百不开！
            8中 7  不开（可控）
            7中 7，6  不开（可控）
            6中 6 ，5 不开（可控）
            5中 5  不开（可控）
            其它随机。
            做成管理后台可控的
            */


            // 1. 统计所有不可开组合（多线程优化，HashSet<int>存储）
            var concurrentBag = new ConcurrentBag<string>();
            Parallel.ForEach(orders, order =>
            {
                for (int win = order.Numbers.Count; win >= 3; win--)
                {
                    var key = $"{order.Numbers.Count}_{win}";
                    if (RuleConfig.TryGetValue(key, out var disabled) && !disabled)
                    {
                        var combinations = ColorNumberUtils.GetCombinations(order.Numbers, win, win);
                        foreach (var comb in combinations)
                        {
                            var combStr = string.Join(",", comb.OrderBy(x => x));
                            concurrentBag.Add(combStr);
                        }
                    }
                }
            });
            // 2. 合并到 HashSet<string>
            var disabledCombinations = new HashSet<string>(concurrentBag);


            // 统计每个数字在禁用组合中出现的频率
            var numberRisk = new Dictionary<int, int>();
            foreach (var comb in disabledCombinations)
            {
                foreach (var n in comb.Split(',').Select(int.Parse))
                {
                    if (!numberRisk.ContainsKey(n)) numberRisk[n] = 0;
                    numberRisk[n]++;
                }
            }

            // 3. 分区选数法，保证分散
            var partitions = new List<List<int>>();
            for (int i = 0; i < 20; i++)
                partitions.Add(Enumerable.Range(i * 4 + 1, 4).ToList());
            var random = new Random(Guid.NewGuid().GetHashCode());
            var seed = partitions.Select(part => part[random.Next(part.Count)]).ToList();
            seed = seed.OrderBy(_ => random.Next()).ToList();

            // 4. 构建候选数字列表，优先分区选数，再低风险优先
            var allNumbers = Numbers.Select(x => x.Number).ToList();
            var allNumbersSorted = seed
                .Concat(allNumbers.Except(seed)
                .GroupBy(n => numberRisk.GetValueOrDefault(n, 0))
                .OrderBy(g => g.Key)
                .SelectMany(g => g.OrderBy(_ => random.Next())))
                .ToList();


            IList<int> result = new List<int>();
            // 最大重试次数
            int maxTryCount = 100000;
            //重试次数
            int tryCount = 0;

            bool found = false;

            object resultLock = new object();

            // 分散性约束：不允许超过maxConsecutive个连续数字
            bool HasTooManyConsecutive(List<int> nums, int maxConsecutive = 3)
            {
                var sorted = nums.OrderBy(x => x).ToList();
                int count = 1;
                for (int i = 1; i < sorted.Count; i++)
                {
                    if (sorted[i] == sorted[i - 1] + 1)
                        count++;
                    else
                        count = 1;
                    if (count > maxConsecutive)
                        return true;
                }
                return false;
            }


            //回溯方法
            // 回溯方法
            bool Dfs(List<int> selected, HashSet<int> used, Random random)
            {
                if (found) return false;
                if (selected.Count == 20)
                    return true;

                // 剩余可选数字不足
                if (allNumbersSorted.Count - used.Count < 20 - selected.Count)
                    return false;

                // 禁用组合校验
                int minLen = 3, maxLen = Math.Min(8, selected.Count);
                if (selected.Count >= minLen)
                {
                    for (int len = minLen; len <= maxLen; len++)
                    {
                        var lastComb = selected.Skip(selected.Count - len).ToList();
                        var combStr = string.Join(",", lastComb.OrderBy(x => x));
                        if (disabledCombinations.Contains(combStr))
                            return false;
                    }
                }

                // 分散性约束
                if (HasTooManyConsecutive(selected, 3)) return false;

                // 优先分散、低风险数字
                var candidates = allNumbersSorted.Where(n => !used.Contains(n)).ToList();
                foreach (var n in candidates)
                {
                    if (Interlocked.Increment(ref tryCount) > maxTryCount)
                        return false;

                    selected.Add(n);
                    used.Add(n);
                    if (Dfs(selected, used, random))
                        return true;
                    selected.RemoveAt(selected.Count - 1);
                    used.Remove(n);
                    if (found) return false;
                }
                return false;
            }
            //多线程并发回溯 根据当前机器的处理器数量进行分片
            Parallel.For(0, Math.Min(Environment.ProcessorCount, 8), (i, state) =>
            {
                var random = new Random(Guid.NewGuid().GetHashCode());
                var startNumbers = allNumbersSorted.OrderBy(_ => random.Next()).ToList();
                foreach (var first in startNumbers)
                {
                    if (found) break;
                    var selected = new List<int> { first };
                    var used = new HashSet<int> { first };
                    if (Dfs(selected, used, random))
                    {
                        lock (resultLock)
                        {
                            if (!found)
                            {
                                result = new List<int>(selected);
                                found = true;
                                state.Stop();
                            }
                        }
                        break;
                    }
                }
            });

            if (!found || result == null)
                throw new Exception("无法生成满足规则的开奖号码，请检查规则配置！");

            var retResult = Shuffle(result).ToList();
            LogMessage.AppendLine($"开奖号码: {string.Join(",", retResult)}");
            return retResult;
        }

        public decimal GetOddsByOption(int numberCount, int winCount)
        {
            var key = $"{numberCount}_{winCount}";
            var odds = 0m;
            OptionMap.TryGetValue(key, out odds);
            return odds;
        }

        public EColor? GetWinColorByNumbers(IList<int> numbers)
        {
            // 统计每种颜色出现的次数
            var colorCounts = Numbers
                .Where(x => numbers.Contains(x.Number))
                .GroupBy(x => x.Color)
                .Select(g => new { Color = g.Key, Count = g.Count() })
                .ToList();

            if (!colorCounts.Any())
                return null;

            // 找到最大出现次数
            int maxCount = colorCounts.Max(x => x.Count);

            // 找到所有达到最大次数的颜色
            var maxColors = colorCounts.Where(x => x.Count == maxCount).ToList();

            // 如果有多个颜色并列最多，返回null，否则返回该颜色
            if (maxColors.Count > 1)
                return null;

            return maxColors[0].Color;
        }

        public int GetWinCount(IList<int> number, IList<int> orderNumbers)
        {
            if (number == null || orderNumbers == null) return 0;
            // 使用 HashSet 提高查找效率
            var numSet = new HashSet<int>(orderNumbers);
            return number.Count(x => numSet.Contains(x));
        }

        public IList<T> Shuffle<T>(IList<T> list)
        {
            Random rng = new Random();
            int n = list.Count;
            while (n > 1)
            {
                n--;
                int k = rng.Next(n + 1);
                T value = list[k];
                list[k] = list[n];
                list[n] = value;
            }
            return list;
        }


    }
}

