﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NetTaste;
using SqlSugar;
using YStudio.Infrastructure;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.Branch.Host.Controllers
{

    /// <summary>
    /// 用户信息
    /// </summary>
    public class UserController : ApiControllerBase
    {

        [Autowired]
        private readonly AccountService branchService;

        [Autowired]
        private readonly UserService userService;

        [Autowired]
        private readonly DbContext dbContext;

        [Autowired]
        protected readonly ICurrentUserService currentUserService;

        /// <summary>
        /// 获取收银员信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ResultBean<Account>> ProfileAsync()
        {
            var result = await branchService.GetProfile(currentUserService.UserId);
            return ResultBean<Account>.Send(result);
        }

        [HttpGet]
        public async Task<ResultBean<UserAssets>> AssetsAsync()
        {
            var result = await userService.GetUserAssetsAsync(currentUserService.UserId);
            return ResultBean<UserAssets>.Send(result);
        }

        [HttpGet]
        public async Task<ResultBean<List<Account>>> Cashiers()
        {
            var list = await dbContext.Accounts.AsQueryable()
                .Where(x => x.BranchId == currentUserService.BranchId)
                .Where(x => x.AccountType == EAccountType.Cashier)
                .IgnoreColumns(o => new { o.LoginPassword })
                .ToListAsync();

            return ResultBean<List<Account>>.Send(list);
        }
    }
}
