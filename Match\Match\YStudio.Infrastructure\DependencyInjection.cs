﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace YStudio.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            services.Configure<ApiBehaviorOptions>(opt =>
            {
                //关闭默认的数据验证400错误
                //opt.SuppressModelStateInvalidFilter = true;
                //统一数据校验返回结构
                opt.InvalidModelStateResponseFactory = context =>
                {
                    var errors = context.ModelState
                      .Where(e => e.Value?.Errors.Count > 0)
                      .Select(e => e.Value?.Errors.First().ErrorMessage)
                      .ToList();
                    return new JsonResult(new { Code = 409, Message = string.Join("|", errors), Data = errors });
                };
            }).AddControllers(options =>
            {
                //关闭不可为空引用类型的属性 防止反序列化时字段为null 不带?的类型报错
                options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true;

                //模型数据校验
                //options.Filters.Add<ModelValidationFilter>();

                //方法权限校验
                //options.Filters.Add<MethodPermissionFilter>();

                //全局异常接管
                options.Filters.Add<ExceptionHandlerFilter>();
            }).AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.Indented;
                //全局处理 返回时间格式
                //options.SerializerSettings.DateFormatString = "yyyy/MM/dd HH:mm:ss";
                //全局处理 接收时间并做本地化处理
                options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Local;
                //首字母小写驼峰式命名
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                //忽略循环引用
                options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                // 空值处理
                //options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;

                options.SerializerSettings.MissingMemberHandling = MissingMemberHandling.Ignore;
            }).AddControllersAsServices();

            return services;
        }
    }
}
