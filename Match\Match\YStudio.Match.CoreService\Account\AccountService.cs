﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure.Core;
using YStudio.Match.Domain;
using YStudio.Match.Domain.Dto.Account;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.CoreService
{
    [Service]
    public class AccountService
    {
        [Autowired]
        protected readonly DbContext dbContext;
        [Autowired]
        protected readonly ICurrentUserService currentUserService;

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="loginAccount">账号</param>
        /// <param name="loginPassword">密码</param>
        /// <param name="channel">渠道</param>
        /// <returns></returns>
        public async Task<Account> LoginAsync(string loginAccount, string loginPassword, string channel)
        {
            var account = await dbContext.Accounts.AsQueryable()
                .Where(x => x.LoginAccount == loginAccount)
                .Where(x => x.LoginPassword == EncodeUtils.ToMd5(loginPassword))
                .Where(x => x.AccountType == EAccountType.Admin || x.AccountType == EAccountType.Agent)
                .IgnoreColumns(o => new { o.LoginPassword })
                .Includes(x => x.Branch)
                .FirstAsync();

            Throws.IfNullOrEmpty(account, "账号不存在或密码错误");
            //状态不为正常的，不允许登录
            Throws.IfTrue(account.Status != ENormalStatus.Normal, "账号已被禁用,请联系管理员");

            await dbContext.Accounts.UpdateAsync(a => new Account
            {
                LastLoginIp = currentUserService.RemoteIP,
                LastLoginTime = DateTime.Now
            }, a => a.Id == account.Id);

            return account;
        }

        public async Task<Account> AddAccount(Account user)
        {
            Throws.IfNullOrEmpty(user, "user is empty");
            using (var uow = dbContext.CreateUnitOfWork(true))
            {

                var exist = await dbContext.Accounts.AsQueryable()
                .Where(x => x.LoginAccount == user.LoginAccount).CountAsync();

                Throws.IfTrue(exist > 0, "Account already exists");
                user.LoginPassword = EncodeUtils.ToMd5(user.LoginPassword);
                user = await dbContext.Accounts.InsertReturnEntityAsync(user);
                await dbContext.UserAssets.InsertAsync(new UserAssets { AccountId = user.Id, Balance = 0, Freeze = 0, BranchId = user.BranchId });
                uow.Commit();
            }
            return user;
        }


        public async Task<PaginatedList<Account>> GetPaginatedListAsync(AccountQueryDto request)
        {

            return await dbContext.Accounts.AsQueryable()
                .Includes(o => o.Branch, b => b.Account.ToList(a => new Account { NickName = a.NickName, Id = a.Id, }))
                .Includes(o => o.Assets)
                .IgnoreColumns(o => new { o.LoginPassword })
                .WhereIF(!string.IsNullOrEmpty(request.LoginAccount), o => o.LoginAccount.Contains(request.LoginAccount))
                .WhereIF(!string.IsNullOrEmpty(request.NickName), o => o.NickName.Contains(request.NickName))
                .WhereIF(request.AccountType.HasValue, o => o.AccountType == request.AccountType.Value)
                .WhereIF(request.BranchId.HasValue, o => o.BranchId == request.BranchId.Value)
                .WhereIF(request.BranchIds?.Count > 0, o => request.BranchIds.Contains(o.BranchId))
                .WhereIF(!string.IsNullOrEmpty(request.BranchName), o => o.Branch.BranchName.Contains(request.BranchName))
                .WhereIF(!string.IsNullOrEmpty(request.AgentName), o => o.Branch.Account.NickName.Contains(request.AgentName))
                .OrderByDescending(o => o.CreateTime)
                .PaginatedListAsync(request.PageIndex, request.PageSize);
        }



        public async Task<Account> GetProfile(int accountId)
        {
            var account = await dbContext.Accounts.AsQueryable()
                  .Includes(x => x.Branch)
                  .Where(x => x.Id == accountId)
                  .IgnoreColumns(x => new { x.LoginPassword })
                  .SingleAsync();

            Throws.IfNullOrEmpty(account, "Account does not exist");

            return account;
        }
    }
}
