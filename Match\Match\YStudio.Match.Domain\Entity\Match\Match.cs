﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{

    [SugarTable("t_match")]
    public class Match
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public string MatchName { get; set; } = string.Empty;

        public int? Interval { get; set; }

        public TimeSpan StartTime { get; set; }

        public TimeSpan EndTime { get; set; }

        public ENormalStatus Status { get; set; }

        public DateOnly? ScheduleDate { get; set; }
    }
}
