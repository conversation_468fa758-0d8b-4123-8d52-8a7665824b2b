﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.CarsRacing
{
    public class CarNumber
    {
        /// <summary>
        /// 数字
        /// </summary>
        public int Number { get; set; }

        /// <summary>
        /// 是否偶数 0 既不是奇数也不是偶数 返回null
        /// </summary>
        public bool? Even { get; set; }

        /// <summary>
        /// 大小
        /// </summary>
        public ESize? Size { get; set; }

        public CarNumber(int number, bool? even, ESize? size)
        {
            Number = number;
            Even = even;
            Size = size;
        }
    }
}
