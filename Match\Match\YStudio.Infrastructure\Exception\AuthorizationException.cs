﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class AuthorizationException : BaseException
    {
        public AuthorizationException(string message) : base(message)
        {
            Code = 401;
        }

        public AuthorizationException(int code, string message) : base(message)
        {
            Code = code;
        }
    }
}
