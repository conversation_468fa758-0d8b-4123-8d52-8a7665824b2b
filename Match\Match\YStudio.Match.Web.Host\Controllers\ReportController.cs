﻿using Microsoft.AspNetCore.Mvc;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Domain.Dto.Account;
using YStudio.Match.Domain.Enums;
using YStudio.Match.Domain.Vo.Report;
using YStudio.Match.Repository;

namespace YStudio.Match.Web.Host.Controllers
{
    public class ReportController : ApiControllerBase
    {

        [Autowired]
        protected readonly ReportService reportService;


        [HttpPost]
        public ResultBean<IList<MatchReportVo>> Query([FromBody] ReportQueryDto reportQueryDto)
        {
            var list = reportService.Query(reportQueryDto);
            return ResultBean<IList<MatchReportVo>>.Send(list);
        }
    }
}
