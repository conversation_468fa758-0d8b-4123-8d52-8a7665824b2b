﻿using Microsoft.Extensions.Logging;
using NetCoreServer;

using System.Net;
using System.Net.Sockets;
using YStudio.Infrastructure.WebSocket;

namespace YStudio.Infrastructure.WebSocket
{
    public class WebSocketServer : WsServer
    {
        private readonly WebSocketSessionManager _sessionManager;

        private readonly ILogger _logger;

        public WebSocketServer(IPAddress address,
            int port,
            WebSocketSessionManager sessionManager,
            ILogger logger) : base(address, port)
        {
            _sessionManager = sessionManager;
            _logger = logger;
        }

        protected override TcpSession CreateSession()
        {
            return new WebSocketSession(this, _sessionManager ,_logger);
        }

        protected override void OnError(SocketError error)
        {
            _logger.LogError($"WebSocket server startup error: {error}");
        }
    }
}
