﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure;

namespace YStudio.Infrastructure
{
    public static class MappingExtensions
    {
        public static Task<PaginatedList<T>> PaginatedListAsync<T>(this ISugarQueryable<T> queryable, int pageIndex, int pageSize, CancellationToken cancellationToken = default(CancellationToken))
        {
            return PaginatedList<T>.CreateAsync(queryable, pageIndex, pageSize, cancellationToken);
        }
    }
}
