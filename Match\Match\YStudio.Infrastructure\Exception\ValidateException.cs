﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class ValidateException : BaseException
    {
        public ValidateException(string message) : base(message)
        {
            Code = 409;
        }

        public ValidateException(int code, string message) : base(message)
        {
            Code = code;
        }
    }
}
