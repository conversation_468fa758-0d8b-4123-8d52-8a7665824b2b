﻿namespace YStudio.Match.SpinWin
{
    public class SpinWinResult
    {
        public int MatchId { get; set; }
        public string Command { get; set; } = "SpinRoundResult";
        public int? CurrentRound { get; set; }
        public DateTime CurrentTime { get; set; }
        public int NextRound { get; set; }
        public DateTime NextTime { get; set; }
        public SpinNumber CurrentNumber { get; set; }
        public List<SpinNumber> RecentResult { get; set; } = new List<SpinNumber>();
    }
}
