﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="YStudio.Match.PrinterService.application" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="Microsoft" asmv2:product="YStudio.Match.PrinterService" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="true" mapFileExtensions="true" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.7.2" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="Application Files\YStudio.Match.PrinterService_1_0_0_0\YStudio.Match.PrinterService.exe.manifest" size="9002">
      <assemblyIdentity name="YStudio.Match.PrinterService.exe" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>6mN22dNFDxpyxBlFTSM6mRA15NuxWsVoFM1BEt2PKYU=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=PC-20240813PQMQ\Administrator" issuerKeyHash="abcf9891428564f9343eeed7087a36a7a3850dc1" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>QWZpO6f620GoDpy0WqoViMZcpGprCtg/qNu1VO+hGR4=</DigestValue></Reference></SignedInfo><SignatureValue>dyupFELuumudvv3HnsLn66UA/GUD6jMYTw9kCbZY0MLAJ6qd6B8dk2nlP/a9izI7/ZmY01ipOAygWG/12WiDEpQONHer8EcIibwJA8ztRWuhWGtA8HqwJpR/htW7cgQR6M3JmsbN8PKpAGPkVvu4ElNlYSQ9hNGKfugj1srKrBM=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>zj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="1e19a1ef54b5dba83fd80a6b6aa45cc68815aa5ab49c0ea841dbfaa73b696641" Description="" Url=""><as:assemblyIdentity name="YStudio.Match.PrinterService.application" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=PC-20240813PQMQ\Administrator</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>3ssDPMnRiAWRaK2vxzJ0gE36fnGR5dXXKTN9x32b95k=</DigestValue></Reference></SignedInfo><SignatureValue>U4SjTPPnlEvKcHI1833MaKj7F35dUDka+/cgXuA07wS7wZn78a0iZQ39/9FLIz5VvyipqZszqQ9fMBSxPFaWlcSpOocGo8QQXlNDOBQ/k4d2sZEbbMH98c57us2evHC5MyD8VBYKjwN26e9hpyutZkosYMBJxgM5pf4GYBlZr0g=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>zj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIICDTCCAXagAwIBAgIQJjxe8bFsYKlJ6zcK0xsdcTANBgkqhkiG9w0BAQsFADBFMUMwQQYDVQQDHjoAUABDAC0AMgAwADIANAAwADgAMQAzAFAAUQBNAFEAXABBAGQAbQBpAG4AaQBzAHQAcgBhAHQAbwByMB4XDTI1MDcwMzA2NTMxMVoXDTI2MDcwMzEyNTMxMVowRTFDMEEGA1UEAx46AFAAQwAtADIAMAAyADQAMAA4ADEAMwBQAFEATQBRAFwAQQBkAG0AaQBuAGkAcwB0AHIAYQB0AG8AcjCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAzj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJECAwEAATANBgkqhkiG9w0BAQsFAAOBgQAyhiRewe3nU9OPAoG5GaRonEi5MEMDXHDAhzgGKzsEnD4xi4oL9a0TXXBYwdf6kKyOCT3kq/aICaj0SC7FVrnbMDai/X4ulP5+R87+4CK7knlS2FWTlUTQ/yZy8SRH+A8bextKs7ks3Fa3WSRogHrJDEkqGpmzfbVpyA0OMnKxKg==</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>