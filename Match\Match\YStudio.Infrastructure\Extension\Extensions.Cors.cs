﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure.Extensions
{
    public static partial class Extensions
    {

        private static string PolicyName = "AllowAll";

        public static void AddAnyCors(this IServiceCollection services)
        {
            //配置跨域
            services.AddCors(c =>
            {
                c.AddPolicy(PolicyName, policy =>
                {
                    policy.SetIsOriginAllowed(origin => true)
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .SetPreflightMaxAge(TimeSpan.FromDays(1))
                    .AllowCredentials();
                });
            });
        }
        public static void UseAnyCors(this WebApplication app)
        {
            app.UseCors(PolicyName);
        }


    }
}
