﻿using YStudio.Infrastructure.WebSocket;

namespace YStudio.Match.Branch.Host.Services
{
    public class WebSocketHostedService : IHostedService, IDisposable
    {

        private readonly WebSocketService _service;

        public WebSocketHostedService(WebSocketService service)
        {
            _service = service;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _service.Start();
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _service?.Stop();
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _service?.Dispose();
        }

    }
}
