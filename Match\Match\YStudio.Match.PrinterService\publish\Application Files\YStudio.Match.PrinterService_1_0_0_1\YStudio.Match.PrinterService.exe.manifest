﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <asmv1:assemblyIdentity name="YStudio.Match.PrinterService.exe" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" type="win32" />
  <application />
  <entryPoint>
    <assemblyIdentity name="YStudio.Match.PrinterService" version="*******" language="neutral" processorArchitecture="msil" />
    <commandLine file="YStudio.Match.PrinterService.exe" parameters="" />
  </entryPoint>
  <trustInfo>
    <security>
      <applicationRequestMinimum>
        <PermissionSet Unrestricted="true" ID="Custom" SameSite="site" />
        <defaultAssemblyRequest permissionSetReference="Custom" />
      </applicationRequestMinimum>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <!--
          UAC 清单选项
          如果要更改 Windows 用户帐户控制级别，请用以下节点之一替换
          requestedExecutionLevel 节点。

        <requestedExecutionLevel  level="asInvoker" uiAccess="false" />
        <requestedExecutionLevel  level="requireAdministrator" uiAccess="false" />
        <requestedExecutionLevel  level="highestAvailable" uiAccess="false" />

          如果要利用文件和注册表虚拟化提供
          向后兼容性，请删除 requestedExecutionLevel 节点。
    -->
        <requestedExecutionLevel level="asInvoker" uiAccess="false" />
      </requestedPrivileges>
    </security>
  </trustInfo>
  <dependency>
    <dependentOS>
      <osVersionInfo>
        <os majorVersion="5" minorVersion="1" buildNumber="2600" servicePackMajor="0" />
      </osVersionInfo>
    </dependentOS>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Windows.CommonLanguageRuntime" version="4.0.30319.0" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ESCPOS.dll" size="16384">
      <assemblyIdentity name="ESCPOS" version="1.3.0.0" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>GvFYu9Gk7B9RFhRcBnC2Lu/MBZO5H2US1FwUbq5h5T4=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Newtonsoft.Json.dll" size="711952">
      <assemblyIdentity name="Newtonsoft.Json" version="********" publicKeyToken="30AD4FE6B2A6AEED" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>4eJ697B+7t9c5xqSVfBCKBam/FhJpIPGcU4bRyBE+p0=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="YStudio.Match.PrinterService.exe" size="36880">
      <assemblyIdentity name="YStudio.Match.PrinterService" version="*******" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>vpC6chf8XjZi9ivf0oL6CKABNKmnkrAVjy9X47hkzpo=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <file name="printer.sdk.dll" size="359936">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>wM5U9rofGrMd960xKAG0ZZ/RM27ylAl8FHZQooALzYU=</dsig:DigestValue>
    </hash>
  </file>
  <file name="YStudio.Match.PrinterService.exe.config" size="163">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>aGKyVzYln3v9NE5D7qEKcDiFvjge7ip0XOsSkWsBoVg=</dsig:DigestValue>
    </hash>
  </file>
<publisherIdentity name="CN=PC-20240813PQMQ\Administrator" issuerKeyHash="abcf9891428564f9343eeed7087a36a7a3850dc1" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>p68TsGCXTUUy5+c6iDNM5NlzxDQGQl/bFM5nS2Jy0TA=</DigestValue></Reference></SignedInfo><SignatureValue>jtG5U3OhB3Jjgx1lXVUMbL9sKGXklOVQ+05jniZ0bIMRdAOmJmkKcXNbHbg9+MdUplpjVNSbwc+iDTWoMHM2Tj1mBGYssg84y02kE5cxldBz7PUV1hqV9feZgL/xHu0CjgB+hmhFw93LrfuzhwXwRG118uwG7zAgGIF1IgWZPWk=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>zj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="30d172624b67ce14db5f420634c473d9e44c33883ae7e732454d9760b013afa7" Description="" Url=""><as:assemblyIdentity name="YStudio.Match.PrinterService.exe" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" type="win32" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=PC-20240813PQMQ\Administrator</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>aUmp/sZ4wym2deYezOzEZEfWXeOVkIEUgGJx67HKkXU=</DigestValue></Reference></SignedInfo><SignatureValue>rBZMtyUDlfFFfvMxVhgsDy0UBkb60JoQ1AMje3E2VYzoWSLoV+feW2fjhNvNfTAkOcuecHCmWlBH9vaoqI+ZFaes3TRbgjwxAWm66f/B+5N2rhrDt8hvC9zynQaX/6rEEm1uNP/hHLgaW67+1Zg1DHLp/bcXmPw6OYOyvG1hR1M=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>zj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIICDTCCAXagAwIBAgIQJjxe8bFsYKlJ6zcK0xsdcTANBgkqhkiG9w0BAQsFADBFMUMwQQYDVQQDHjoAUABDAC0AMgAwADIANAAwADgAMQAzAFAAUQBNAFEAXABBAGQAbQBpAG4AaQBzAHQAcgBhAHQAbwByMB4XDTI1MDcwMzA2NTMxMVoXDTI2MDcwMzEyNTMxMVowRTFDMEEGA1UEAx46AFAAQwAtADIAMAAyADQAMAA4ADEAMwBQAFEATQBRAFwAQQBkAG0AaQBuAGkAcwB0AHIAYQB0AG8AcjCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAzj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJECAwEAATANBgkqhkiG9w0BAQsFAAOBgQAyhiRewe3nU9OPAoG5GaRonEi5MEMDXHDAhzgGKzsEnD4xi4oL9a0TXXBYwdf6kKyOCT3kq/aICaj0SC7FVrnbMDai/X4ulP5+R87+4CK7knlS2FWTlUTQ/yZy8SRH+A8bextKs7ks3Fa3WSRogHrJDEkqGpmzfbVpyA0OMnKxKg==</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>