﻿namespace YStudio.Infrastructure
{
    public class DateTimeUtils
    {
        /// <summary>
        /// 获取当前时间的秒级时间戳（自1970-01-01 00:00:00 UTC起的秒数）。
        /// </summary>
        public static long GetCurrentTimestampSeconds()
        {
            return DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }

        /// <summary>
        /// 获取当前时间的毫秒级时间戳（自1970-01-01 00:00:00 UTC起的毫秒数）。
        /// </summary>
        public static long GetCurrentTimestampMilliseconds()
        {
            return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        }

        /// <summary>
        /// 将秒级时间戳转换为本地时间（DateTime）。
        /// </summary>
        public static DateTime ParseTimestampSeconds(long seconds)
        {
            return DateTimeOffset.FromUnixTimeSeconds(seconds).ToLocalTime().DateTime;
        }

        /// <summary>
        /// 将毫秒级时间戳转换为本地时间（DateTime）。
        /// </summary>
        public static DateTime ParseTimestampMilliseconds(long milliseconds)
        {
            return DateTimeOffset.FromUnixTimeMilliseconds(milliseconds).ToLocalTime().DateTime;
        }

        /// <summary>
        /// 将 DateTime 转换为秒级时间戳（自1970-01-01 00:00:00 UTC起的秒数）。
        /// </summary>
        public static long ToTimestampSeconds(DateTime dateTime)
        {
            return new DateTimeOffset(dateTime.ToUniversalTime()).ToUnixTimeSeconds();
        }

        /// <summary>
        /// 将 DateTime 转换为毫秒级时间戳（自1970-01-01 00:00:00 UTC起的毫秒数）。
        /// </summary>
        public static long ToTimestampMilliseconds(DateTime dateTime)
        {
            return new DateTimeOffset(dateTime.ToUniversalTime()).ToUnixTimeMilliseconds();
        }
    }
}
