﻿using Nito.AsyncEx;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.CoreService
{
    public static class AsyncLocker
    {
        public static readonly AsyncLock GenerateRoundLocker = new();

        public static readonly AsyncLock LuckyRoulettenRoundLocker = new();

        public static readonly AsyncLock CarsRacingRoundLocker = new();

        public static readonly AsyncLock ColorLuckyRoundLocker = new();

        public static readonly AsyncLock OrderOperationLocker = new();


    }
}
