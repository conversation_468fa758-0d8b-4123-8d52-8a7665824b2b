﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Repository;
using YStudio.Match.Web.Host.Controllers;

namespace YStudio.Match.Web.Host.Controllers
{
    public class BranchController : ApiControllerBase
    {

        [Autowired]
        protected readonly BranchService branchService;

        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly ICurrentUserService currentUserService;


        [HttpPost]
        public async Task<PaginatedList<Branch>> Fetch([FromBody] BranchQueryDto request)
        {
            if (currentUserService.AccountType == 20)
            {
                request.AccountId = currentUserService.UserId;
            }

            return await dbContext.Branchs.AsQueryable()
                .Includes(x => x.Account)
                .WhereIF(request.AccountId.HasValue, x => x.AccountId == request.AccountId)
                .WhereIF(!string.IsNullOrEmpty(request.BranchName), x => x.BranchName.Contains(request.BranchName))
                .WhereIF(!string.IsNullOrEmpty(request.ContactName), x => x.ContactName.Contains(request.ContactName))
                .WhereIF(!string.IsNullOrEmpty(request.NickName), x => x.Account.NickName.Contains(request.NickName))
                .WhereIF(!string.IsNullOrEmpty(request.Address), x => x.Address.Contains(request.Address))
                .OrderByDescending(x => x.CreateTime)
                .PaginatedListAsync(request.PageIndex, request.PageSize);
        }

        [HttpPost]
        public async Task<ResultBean<Branch>> Update([FromBody] Branch branch)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");
            var res = await dbContext.Branchs.UpdateAsync(branch);
            return ResultBean<Branch>.Send(branch);
        }

        [HttpPost]
        public async Task<ResultBean<Branch>> Create([FromBody] Branch branch)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");
            var res = await dbContext.Branchs.InsertAsync(branch);
            return ResultBean<Branch>.Send(branch);
        }
    }
}
