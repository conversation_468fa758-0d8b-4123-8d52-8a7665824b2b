<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FreeRedis" Version="1.3.6" />
    <PackageReference Include="FreeScheduler" Version="2.0.35" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.36" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.36" />
    <PackageReference Include="NAutowired.Core" Version="2.3.1" />
    <PackageReference Include="NetCoreServer" Version="6.7.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SqlSugarCoreNoDrive" Version="5.1.4.171" />
  </ItemGroup>

</Project>
