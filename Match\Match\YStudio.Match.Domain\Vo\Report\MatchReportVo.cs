﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain.Vo.Report
{
    public class MatchReportVo
    {
        public int MatchId { get; set; }
        public string MatchName { get; set; } = string.Empty;
        public decimal? TotalStake { get; set; }
        public decimal? TotalPayout { get; set; }
        public decimal? TotalPaid { get; set; }
        public decimal? TotalUnPaid { get; set; }
        public decimal? Profit { get; set; }
        public decimal? ProfitRate { get; set; }
    }
}
