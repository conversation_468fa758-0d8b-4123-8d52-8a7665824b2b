﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.BranchClient
{
    [ComVisible(true)]
    public class JsBridge
    {

        public void Test(string par1, dynamic jsCallback)
        {
            if (jsCallback != null)
            {
                jsCallback();
            }
        }
    }
}
