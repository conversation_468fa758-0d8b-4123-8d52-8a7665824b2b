﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.Domain
{
    [SugarTable("t_account")]
    public class Account : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public string? NickName { get; set; }

        public string? LoginAccount { get; set; }

        public string? LoginPassword { get; set; }

        public string? Mobile { get; set; }

        public string? LastLoginIp { get; set; }

        public EAccountType AccountType { get; set; }

        public DateTime LastLoginTime { get; set; }

        public ENormalStatus Status { get; set; }

        public DateTime? EndTime { get; set; }

        [Navigate(NavigateType.OneToOne, nameof(Account.Id), nameof(UserAssets.AccountId))]
        public UserAssets? Assets { get; set; }

        public int BranchId { get; set; }
        [Navigate(NavigateType.OneToOne, nameof(BranchId), nameof(Domain.Branch.Id))]
        public Branch? Branch { get; set; }
    }
}
