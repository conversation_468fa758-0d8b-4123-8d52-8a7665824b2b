﻿using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure.Authorization
{
    public class JwtHelper
    {
        /// <summary>
        /// 签发Token
        /// </summary>
        /// <param name="claims"></param>
        /// <returns></returns>
        public static string buildToken(IEnumerable<Claim> claims, DateTime expireAt)
        {
            var settings = InternalApp.Configuration.GetSection("JwtSettings").Get<JwtSettings>();

            //加密的token密钥
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(settings.SecretKey));

            //签名证书，其值为securityKey和HmacSha256Signature算法
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256Signature);

            //表示jwt token的描述信息，其值包括Issuer签发方，Audience接收方，Claims载荷，过期时间和签名证书
            var tokenDescriptor = new JwtSecurityToken(
                issuer: settings.Issuer,
                audience: settings.Audience,
                claims: claims,
                expires: expireAt,
                signingCredentials: credentials);

            return new JwtSecurityTokenHandler().WriteToken(tokenDescriptor);

        }


    }
}
