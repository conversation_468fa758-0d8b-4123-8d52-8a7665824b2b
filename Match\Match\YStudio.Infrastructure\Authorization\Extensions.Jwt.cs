﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace YStudio.Infrastructure.Authorization
{
    public static partial class Extensions
    {
        public static void AddJwt(this IServiceCollection services)
        {
            var settings = services.BuildServiceProvider()
                .GetRequiredService<IConfiguration>()
                .GetSection("JwtSettings")
                .Get<JwtSettings>();

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(o =>
            {
                o.TokenValidationParameters = new TokenValidationParameters
                {
                    // 验证签名密钥
                    ValidateIssuerSigningKey = false,
                    // 验证发行者
                    ValidateIssuer = false,
                    // 验证受众
                    ValidateAudience = false,
                    // 发行者
                    ValidIssuer = settings.Issuer,
                    // 受众
                    ValidAudience = settings.Audience,
                    // 签名密钥
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(settings.SecretKey)),
                    //校验有效期,使用当前时间与Token的Claims中的NotBefore和Expires对比
                    ValidateLifetime = false,
                    //允许时间误差
                    // ClockSkew = TimeSpan.FromMinutes(1),
                    RequireExpirationTime = false,
                };

                //o.Events = new JwtBearerEvents
                //{
                //    OnMessageReceived = context =>
                //    {
                //        //var accessToken = context.Request.Query["access_token"];
                //        //_ = context.HttpContext.Request.Path;

                //        //if (!string.IsNullOrEmpty(accessToken))
                //        //    context.Token = accessToken;
                //        return Task.CompletedTask;
                //    },
                //    OnAuthenticationFailed = context =>
                //    {
                //        //// 如果过期，把过期信息添加到头部
                //        //if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                //        //{
                //        //    Console.WriteLine("jwt过期了");
                //        //    context.Response.Headers.Add("Token-Expired", "true");
                //        //}
                //        return Task.CompletedTask;
                //    },
                //    OnChallenge = context =>
                //    {
                //        return Task.CompletedTask;
                //    },
                //    OnForbidden = context =>
                //    {
                //        return Task.CompletedTask;
                //    }
                //};
            });

            // IAuthorizationMiddlewareResultHandler 用来替换框架默认的授权返回结果
            services.AddSingleton<IAuthorizationMiddlewareResultHandler, AuthorizationMiddlewareResultHandler>();
        }
    }
}
