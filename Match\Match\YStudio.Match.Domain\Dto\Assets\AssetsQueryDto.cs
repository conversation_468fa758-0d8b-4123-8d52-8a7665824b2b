﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    public class AssetsQueryDto : PageableRequest
    {
        
        public int? FromId { get; set; }
        public string? FromAccount { get; set; }
        public string? ToAccount { get; set; }
        public string? AgentName { get; set; }

        public string? ToBranchName { get; set; }
        public string? Remark { get; set; }
        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }


    }
}
