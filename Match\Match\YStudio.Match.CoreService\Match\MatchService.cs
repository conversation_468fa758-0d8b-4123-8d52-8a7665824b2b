﻿using FreeScheduler;
using Microsoft.AspNetCore.Routing;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Match.Domain;
using static FreeSql.Internal.GlobalFilter;

namespace YStudio.Match.CoreService
{

    [Service]
    public class MatchService
    {
        [Autowired]
        protected readonly DbContext dbContext;


        /// <summary>
        /// 获取指定比赛的最近有结果的期数
        /// </summary>
        /// <param name="matchId"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        public async Task<IList<MatchRound>> GetRecentRoundsAsync(int matchId, int limit)
        {
            var list = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.MatchId == matchId)
                .Where(r => r.EndTime <= DateTime.Now)
                .Take(limit)
                .Where(r => !string.IsNullOrEmpty(r.Result))
                .Where(r => r.Status == ERoundStatus.Completed)
                .OrderByDescending(r => r.EndTime)
                .ToListAsync();

            return list;
        }

        /// <summary>
        /// 获取指定比赛的下一期 倘若没有则取明天第一期时间
        /// </summary>
        /// <param name="matchId"></param>
        /// <param name="now"></param>
        /// <returns></returns>
        public async Task<MatchRound> GetNextRoundAsync(int matchId, DateTime now)
        {
            var nextRound = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.MatchId == matchId)
                .Where(r => r.EndTime > now)
                .Where(r => r.StartTime <= now)
                .Where(r => r.Status == ERoundStatus.Pending)
                .OrderByDescending(r => r.EndTime)
                .FirstAsync();

            //当前时间没有正在进行的期数，取最接近的下一期
            if (nextRound == null)
            {
                nextRound = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.MatchId == matchId)
                .Where(r => r.EndTime > now)
                .Where(r => r.Status == ERoundStatus.Pending)
                .OrderBy(r => r.StartTime)
                .FirstAsync();
            }

            if (nextRound == null)
            {
                var match = await dbContext.Matchs.AsQueryable().Where(m => m.Id == matchId).FirstAsync();
                nextRound = new MatchRound();
                nextRound.Round = 1;
                nextRound.EndTime = now.Date.AddDays(1).Add(match.StartTime).AddSeconds(Convert.ToDouble(match.Interval));
            }

            return nextRound;
        }

        /// <summary>
        /// 获取当前比赛期数
        /// </summary>
        /// <param name="matchId"></param>
        /// <param name="now"></param>
        /// <returns></returns>
        public async Task<MatchRound> GetCurrentRoundAsync(int matchId, DateTime now)
        {

            //当前时间没有正在进行的期数，则取下一期
            var nextRound = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.MatchId == matchId)
                .Where(r => r.EndTime > now)
                .Where(r => r.StartTime <= now)
                .OrderByDescending(r => r.EndTime)
                .FirstAsync();

            //当前时间没有正在进行的期数，取最接近的下一期
            if (nextRound == null)
            {
                nextRound = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.MatchId == matchId)
                .Where(r => r.EndTime > now)
                .Where(r => r.Status == ERoundStatus.Pending)
                .OrderBy(r => r.EndTime)
                .FirstAsync();
            }

            //如果还是没有，说明今天没有比赛，取明天第一期
            if (nextRound == null)
            {
                var match = await dbContext.Matchs.AsQueryable().Where(m => m.Id == matchId).FirstAsync();
                nextRound = new MatchRound();
                nextRound.Round = 1;
                nextRound.EndTime = now.Date.AddDays(1).Add(match.StartTime).AddSeconds(Convert.ToDouble(match.Interval));
            }

            return nextRound;
        }


        public async Task<List<MatchOption>> GetOptionsAsync()
        {
            return await dbContext.MatchOptions.AsQueryable()
                .Where(o => o.RecordState == 0)
                .ToListAsync();
        }



        /// <summary>
        /// 获取当天的期数任务
        /// </summary>
        /// <returns></returns>
        public async Task<List<MatchRound>> findCurrentDateMatchRounds()
        {
            string day = DateTime.Now.ToString("yyyy-MM-dd");
            String stime = day + " 00:30:00";
            String etime = day + " 23:30:00";
            return await dbContext.MatchRounds.AsQueryable()
                .Where(m => m.StartTime >= DateTime.Parse(stime))
                .Where(m => m.StartTime <= DateTime.Parse(etime))
                .ToListAsync();
        }

        public async Task GetMatchRounds()
        {
            //var matchs = await dbContext.Matchs.AsQueryable()
            //      .Where(m => m.Status == 1)
            //     .ToListAsync();

            //matchs.ForEach(async m =>
            //{

            //    DateTime currentDate = DateTime.Now.Date;

            //    //生成当天的
            //    if (m.ScheduleDate == null)
            //    {
            //        DateTime startTime = currentDate.Add(m.StartTime);
            //        DateTime endTime = currentDate.AddDays(m.EndTime < m.StartTime ? 1 : 0).Add(m.EndTime);

            //        int round = 1;
            //        var rounds = new List<MatchRound>();
            //        while (startTime < endTime)
            //        {
            //            var item = new MatchRound
            //            {
            //                MatchId = m.Id,
            //                Round = round,
            //                StartTime = startTime,
            //                Interval = m.Interval,
            //                EndTime = startTime.AddSeconds(Convert.ToDouble(m.Interval)),
            //                CreateTime = DateTime.Now
            //            };
            //            rounds.Add(item);
            //            round++;
            //            startTime = item.EndTime.Value;
            //        }
            //        await dbContext.MatchRounds.InsertRangeAsync(rounds);
            //        m.ScheduleDate = currentDate;
            //        await dbContext.Matchs.AsUpdateable(m).UpdateColumns(m => m.ScheduleDate).ExecuteCommandAsync();
            //    }


            //    //生成明天的
            //    if (currentDate == m.ScheduleDate)
            //    {
            //        currentDate = currentDate.AddDays(1);

            //        DateTime startTime = currentDate.Add(m.StartTime);
            //        DateTime endTime = currentDate.AddDays(m.EndTime < m.StartTime ? 1 : 0).Add(m.EndTime);

            //        var lastRound = await dbContext.MatchRounds.AsQueryable()
            //            .Where(r => r.MatchId == m.Id)
            //            .OrderByDescending(r => r.EndTime)
            //            .FirstAsync();

            //        long round = (lastRound == null) ? lastRound.Round + 1 : 1;

            //        var rounds = new List<MatchRound>();
            //        while (startTime < endTime)
            //        {
            //            var item = new MatchRound
            //            {
            //                MatchId = m.Id,
            //                Round = round,
            //                StartTime = startTime,
            //                Interval = m.Interval,
            //                EndTime = startTime.AddSeconds(Convert.ToDouble(m.Interval)),
            //                CreateTime = DateTime.Now
            //            };
            //            rounds.Add(item);
            //            round++;
            //            startTime = item.EndTime.Value;
            //        }
            //        await dbContext.MatchRounds.InsertRangeAsync(rounds);
            //        m.ScheduleDate = currentDate;
            //        await dbContext.Matchs.AsUpdateable(m).UpdateColumns(m => m.ScheduleDate).ExecuteCommandAsync();
            //    }
            //});

        }

        /// <summary>
        /// 获取指定比赛的轮次列表
        /// </summary>
        /// <param name="matchId"></param>
        /// <returns></returns>
        public async Task<List<MatchRound>> GetRoundsAsync(int matchId)
        {
            var now = DateTime.Now;
            return await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.MatchId == matchId)
                .Where(r => r.EndTime > now)
                .Where(r => r.Status == ERoundStatus.Pending)
                //只显示当天的
                .Where(r => r.EndTime < now.Date.AddDays(1))
                .OrderBy(r => r.StartTime)
                .ToListAsync();
        }

        public async Task<List<Domain.Match>> GetList()
        {
            return await dbContext.Matchs.AsQueryable().OrderBy(m => m.Id).ToListAsync();
        }
    }
}
