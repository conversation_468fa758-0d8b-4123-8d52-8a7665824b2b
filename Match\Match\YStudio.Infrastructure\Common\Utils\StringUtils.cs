﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class StringUtils
    {
        /// <summary>
        /// 解析URL中的QueryString为字典
        /// </summary>
        public static Dictionary<string, string> ParseQueryString(string url)
        {
            var result = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            if (string.IsNullOrEmpty(url))
                return result;

            // 查找问号，获取query部分
            int idx = url.IndexOf('?');
            string query = idx >= 0 ? url.Substring(idx + 1) : url;

            foreach (var pair in query.Split('&', StringSplitOptions.RemoveEmptyEntries))
            {
                var kv = pair.Split('=', 2);
                var key = Uri.UnescapeDataString(kv[0]);
                var value = kv.Length > 1 ? Uri.UnescapeDataString(kv[1]) : string.Empty;
                if (!string.IsNullOrEmpty(key))
                    result[key] = value;
            }
            return result;
        }
    }
}
