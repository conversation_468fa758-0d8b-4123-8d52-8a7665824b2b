﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.ColorLucky
{
    internal class Test
    {
        public static List<string> FindBestPayoutCombinations(
    Dictionary<string, decimal> payoutMap, decimal targetAmount, int maxNumbers = 20)
        {
            // 构建所有 payoutMap key 的 HashSet<int> 形式，便于后续判断
            var allKeySets = payoutMap.Keys
                .Select(k => new HashSet<int>(k.Split(',').Select(int.Parse)))
                .ToList();

            // 排序：优先3-6个数字的组合，其次其它，内部再按单位金额降序
            var sorted = payoutMap
                .Select(x => new
                {
                    Key = x.Key,
                    Value = x.Value,
                    Numbers = x.Key.Split(',').Select(int.Parse).ToList(),
                    Count = x.Key.Split(',').Length,
                    Score = x.Value / x.Key.Split(',').Length
                })
                .Where(x => x.Count <= maxNumbers && x.Value > 0 && x.Count != 7 && x.Count != 8)
                .OrderByDescending(x => x.Count >= 3 && x.Count <= 6) // 3-6个数优先
                .ThenByDescending(x => x.Score) // 单位金额次之
                .ToList();

            var bestResult = new List<string>();
            decimal bestSum = 0;

            void Backtrack(int idx, List<string> current, HashSet<int> used, decimal sum, int numCount)
            {
                if (sum > targetAmount || numCount > maxNumbers) return;

                // 检查当前已选数字集合是否命中 payoutMap 其它组合（非自身）
                if (current.Count > 0)
                {
                    var currentSet = new HashSet<int>(used);
                    foreach (var keySet in allKeySets)
                    {
                        if (current.Any(sel => new HashSet<int>(sel.Split(',').Select(int.Parse)).SetEquals(keySet)))
                            continue;
                        if (currentSet.SetEquals(keySet))
                            return;
                    }
                }

                if (sum > bestSum)
                {
                    bestSum = sum;
                    bestResult = new List<string>(current);
                }

                for (int i = idx; i < sorted.Count; i++)
                {
                    var item = sorted[i];
                    if (item.Numbers.Any(n => used.Contains(n))) continue;

                    current.Add(item.Key);
                    foreach (var n in item.Numbers) used.Add(n);

                    Backtrack(i + 1, current, used, sum + item.Value, numCount + item.Numbers.Count);

                    current.RemoveAt(current.Count - 1);
                    foreach (var n in item.Numbers) used.Remove(n);
                }
            }

            Backtrack(0, new List<string>(), new HashSet<int>(), 0, 0);
            return bestResult;
        }


        public static List<string> FindBestPayoutCombinations2(
    Dictionary<string, decimal> payoutMap, decimal targetAmount, int maxNumbers = 20)
        {
            // 预处理：去除无效项，按单位数字金额降序排序
            var sorted = payoutMap
                .Select(x => new
                {
                    Key = x.Key,
                    Value = x.Value,
                    Numbers = x.Key.Split(',').Select(int.Parse).ToList(),
                    Score = x.Value / x.Key.Split(',').Length
                })
                .Where(x => x.Numbers.Count <= maxNumbers && x.Value > 0)
                .OrderByDescending(x => x.Score)
                .ToList();

            var bestResult = new List<string>();
            decimal bestSum = 0;

            void Backtrack(int idx, List<string> current, HashSet<int> used, decimal sum, int numCount)
            {
                if (sum > targetAmount || numCount > maxNumbers) return;
                if (sum > bestSum)
                {
                    bestSum = sum;
                    bestResult = new List<string>(current);
                }
                for (int i = idx; i < sorted.Count; i++)
                {
                    var item = sorted[i];
                    if (item.Numbers.Any(n => used.Contains(n))) continue;

                    // 剪枝：剩余最大金额都无法超越当前最优解
                    decimal remainMax = 0;
                    int remainNum = numCount;
                    for (int j = i; j < sorted.Count; j++)
                    {
                        if (!sorted[j].Numbers.Any(n => used.Contains(n)))
                        {
                            if (remainNum + sorted[j].Numbers.Count > maxNumbers) break;
                            remainMax += sorted[j].Value;
                            remainNum += sorted[j].Numbers.Count;
                        }
                    }
                    if (sum + remainMax <= bestSum) break;

                    current.Add(item.Key);
                    foreach (var n in item.Numbers) used.Add(n);

                    Backtrack(i + 1, current, used, sum + item.Value, numCount + item.Numbers.Count);

                    current.RemoveAt(current.Count - 1);
                    foreach (var n in item.Numbers) used.Remove(n);
                }
            }

            Backtrack(0, new List<string>(), new HashSet<int>(), 0, 0);
            return bestResult;
        }

    }
}
