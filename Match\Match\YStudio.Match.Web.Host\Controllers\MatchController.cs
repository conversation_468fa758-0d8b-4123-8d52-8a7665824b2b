﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Repository;
using YStudio.Match.Web.Host.Controllers;
using YStudio.Match.Web.Host.Services;

namespace YStudio.Match.Web.Host.Controllers
{
    public class MatchController : ApiControllerBase
    {
        [Autowired]
        protected readonly MatchService matchService;


        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly ICurrentUserService currentUserService;

        [HttpGet]
        public async Task<ResultBean<List<Domain.Match>>> List()
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");
            var result = await matchService.GetList();
            return ResultBean<List<Domain.Match>>.Send(result);
        }

        [HttpPost]
        public async Task<ResultBean<Domain.Match>> Update([FromBody] Domain.Match match)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");
            var res = await dbContext.Matchs.UpdateAsync(match);
            return ResultBean<Domain.Match>.Send(match);
        }



    }
}
