﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NAutowired.Core.Extensions;
using NetCoreServer;
using System;
using System.Collections.Concurrent;
using System.Drawing;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HttpRequest = NetCoreServer.HttpRequest;


namespace YStudio.Infrastructure.WebSocket
{
    public class WebSocketSession : WsSession
    {
        private readonly WebSocketSessionManager _sessionManager;

        private readonly ILogger _logger;

        public WebSocketSession(WsServer server,
            WebSocketSessionManager sessionManager,
            ILogger logger)
            : base(server)
        {
            _sessionManager = sessionManager;
            _logger = logger;
        }

        public override async void OnWsConnected(HttpRequest request)
        {
            _sessionManager.Add(this, request);
            _logger.LogInformation($"WebSocket session {Id} connected.");

            try
            {
                using (var scope = App.ServiceProvider.CreateAsyncScope())
                {
                    var messageHandler = scope.ServiceProvider.GetServiceWithAutowired<IMessageHandler>();
                    await messageHandler.OnConnectedAsync(this, request);
                }
            }
            catch (Exception ex)
            {
                Close(-1);
                _logger.LogError(ex, $"Error processing message from {Id}");
            }

        }

        public override void OnWsDisconnected()
        {
            _sessionManager.Remove(Id);
            _logger.LogInformation($"WebSocket session {Id} disconnected.");
        }

        public override async void OnWsReceived(byte[] buffer, long offset, long size)
        {
            try
            {
                var message = Encoding.UTF8.GetString(buffer, (int)offset, (int)size);
                _logger.LogDebug($"Received message from {Id}: {message}");

                using (var scope = App.ServiceProvider.CreateAsyncScope())
                {
                    var messageHandler = scope.ServiceProvider.GetServiceWithAutowired<IMessageHandler>();
                    await messageHandler.HandleMessageAsync(this, message);
                }
            }
            catch (Exception ex)
            {
                Close(-1);
                _logger.LogError(ex, $"Error processing message from {Id}");
            }
        }

        protected override void OnError(SocketError error)
        {
            _sessionManager.Remove(Id);
            _logger.LogError($"WebSocket session {Id} error: {error}");
        }
    }
}
