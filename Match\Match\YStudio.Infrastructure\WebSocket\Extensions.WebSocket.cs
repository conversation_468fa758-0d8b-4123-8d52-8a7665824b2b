﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NAutowired.Core.Extensions;
using System.Net;
using YStudio.Infrastructure.WebSocket;

namespace YStudio.Infrastructure.Extensions
{
    public static partial class Extensions
    {

        /// <summary>
        /// 注册WebSocket相关服务
        /// </summary>
        /// <param name="services"></param>
        /// <param name="address">监听地址</param>
        /// <param name="port">监听端口</param>
        public static void AddWebSocket(this IServiceCollection services, IPAddress address, int port)
        {
            services.AddSingleton<WebSocketSessionManager>();
            services.AddSingleton<WebSocketService>(sp =>
            {
                var sessionManager = sp.GetServiceWithAutowired<WebSocketSessionManager>();
                var logger = sp.GetRequiredService<ILogger<WebSocketService>>();
                return new WebSocketService(address, port, sessionManager, logger);
            });


        }
    }
}
