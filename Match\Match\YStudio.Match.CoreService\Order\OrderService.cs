﻿using FreeScheduler;
using Microsoft.Extensions.Options;
using NetTaste;
using Nito.AsyncEx;
using System.Reflection;
using System.Threading;
using YStudio.Match.Domain;

namespace YStudio.Match.CoreService
{
    [Service]
    public class OrderService
    {
        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly ICurrentUserService currentUserService;


        public async Task<Order> AddAsync(Order order)
        {

            Throws.IfNullOrEmpty(order, "selection is empty");
            Throws.IfNullOrEmpty(order.Items, "ticket selections is empty");

            /***
             * TODO
             * 校验每一项的最大金额 和最小金额是否符合要求
             * 
             */
            using (var uow = dbContext.CreateUnitOfWork(true))
            {

                var match = await dbContext.Matchs.AsQueryable()
                  .Where(m => m.Id == order.MatchId)
                  .Where(m => m.Status == ENormalStatus.Normal)
                  .FirstAsync();

                Throws.IfNullOrEmpty(match, "match is not allow bet");

                var branch = await dbContext.Branchs.AsQueryable()
                    .Where(b => b.Id == currentUserService.BranchId)
                    .FirstAsync();

                Throws.IfNullOrEmpty(branch, "branch is not exist");

                order.Id = Guid.NewGuid();
                order.OrderNo = GenerateOrderNO();
                order.CreateTime = DateTime.Now;
                order.Status = EOrderState.Submitted;

                //校验Round当前是否可以下单

                var now = DateTime.Now;

                var round = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.MatchId == order.MatchId)
                .WhereIF(!order.RoundId.HasValue, r => r.StartTime <= now)
                .Where(r => r.EndTime > now)
                .WhereIF(order.RoundId.HasValue, r => r.Id == order.RoundId)
                .Where(r => r.Status == ERoundStatus.Pending)
                .FirstAsync();

                Throws.IfNullOrEmpty(round, "Round selection is incorrect");

                order.EndTime = round.EndTime;
                order.Round = round.Round;
                order.RoundId = round.Id;

                order.MatchName = match.MatchName;
                order.Cashier = currentUserService.NickName;
                order.CashierId = currentUserService.UserId;
                order.BranchId = branch.Id;
                order.BranchName = branch.BranchName;
                order.ActualPayout = 0;
                order.Stake = 0;
                order.MaxPayout = 0;
                order.ExpireTime = null;
                order.PrintTime = null;
                order.CancelTime = null;
                order.PayTime = null;



                /**
                 *  TODO 该操作需缓存下来
                 */
                var options = await dbContext.MatchOptions
                    .AsQueryable()
                    .Where(o => o.RecordState == 0)
                    .ToListAsync();

                //var newOrder = await dbContext.Orders.InsertReturnEntityAsync(order);

                foreach (var item in order.Items)
                {
                    var option = options
                    .Where(o => o.MatchId == order.MatchId)
                    .Where(o => o.Id == item.OptionId)
                    .FirstOrDefault();


                    item.Id = Guid.NewGuid();
                    item.OrderId = order.Id;
                    item.MatchId = order.MatchId;
                    item.RoundId = round.Id;
                    item.ActualPayout = 0;

                    if (order.MatchId != EMatch.ColorLucky)
                    {
                        Throws.IfNullOrEmpty(option, "option is empty");
                        item.Odds = option.Odds;
                        item.OptionName = option.OptionName;
                        item.OptionType = option.Type;
                    }

                    item.MaxPayout = item.Stake * item.Odds;
                    item.Status = EOrderState.Submitted;
                    item.CancelTime = null;
                    order.Stake += item.Stake;
                    order.MaxPayout += item.MaxPayout;
                }

                var userAssets = await dbContext.UserAssets.AsQueryable().Where(a => a.AccountId == currentUserService.UserId)
                    .Where(a => a.BranchId == order.BranchId)
                    .FirstAsync();

                Throws.IfNullOrEmpty(userAssets, "user assets is not exist");
                Throws.If(userAssets.Balance < order.Stake, "balance is not enough");
                userAssets.Balance -= order.Stake;
                await dbContext.UserAssets.AsUpdateable(userAssets)
                     .UpdateColumns(a => new { a.Balance })
                     .ExecuteCommandAsync();



                var newOrder = await dbContext.Db.InsertNav(order)
                     .Include(o => o.Items)
                     .ExecuteReturnEntityAsync();

                uow.Commit();

                return newOrder;
            }

        }


        /// <summary>
        /// 查询指定游戏,指定期数的订单项，按选项分组统计
        /// </summary>
        /// <param name="matchId"></param>
        /// <param name="roundId"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, decimal>> GetOrderItemsGroupByOptionAsync(int matchId, Guid roundId)
        {
            // SELECT optionId, optionName, Count(1), SUM(stake), SUM(payout) FROM t_order_item GROUP BY optionId  
            var result = await dbContext.OrderItems.AsQueryable()
                .Where(i => i.RoundId == roundId)
                .Where(i => i.MatchId == matchId)
                .Where(i => i.Status == EOrderState.Submitted)
                .GroupBy(i => new { i.OptionId, i.OptionName })
                .Select(g => new
                {
                    Key = $"{g.OptionId}_{g.OptionName}",
                    TotalStake = SqlFunc.AggregateSum(g.Stake)
                })
                .ToListAsync();

            return result.ToDictionary(item => item.Key, item => item.TotalStake);
        }



        /// <summary>
        /// 查询指定游戏,指定期数的订单
        /// </summary>
        /// <param name="roundId"></param>
        /// <returns></returns>
        public async Task<List<OrderItem>> GetOrderItemsAsync(int matchId, Guid roundId)
        {
            var list = await dbContext.OrderItems.AsQueryable()
                  .Where(i => i.RoundId == roundId)
                  .Where(i => i.MatchId == matchId)
                  .Where(i => i.Status == EOrderState.Submitted)
                  .OrderBy(i => i.CreateTime)
                  .ToListAsync();
            return list;
        }

        /// <summary>
        /// 查询指定期数的订单列表
        /// </summary>
        /// <param name="roundId"></param>
        /// <returns></returns>
        public async Task<IList<Order>> GetOrdersByRoundIdAsync(Guid roundId)
        {
            return await dbContext.Orders.AsQueryable()
                .Includes(o => o.Items)
                .Where(o => o.RoundId == roundId)
                .OrderByDescending(o => o.CreateTime)
                .ToListAsync();
        }


        /// <summary>
        /// 更新指定期数的订单状态和实际赔付金额
        /// </summary>
        /// <param name="roundId"></param>
        /// <returns></returns>
        public async Task<bool> UpdateOrderStatusByRound(Guid roundId)
        {
            return await dbContext.Orders.AsUpdateable()
                .SetColumns(t_order => new Order
                {
                    // 统计所有OrderItem的ActualPayout总和
                    ActualPayout = SqlFunc.IsNull(SqlFunc.Subqueryable<Domain.OrderItem>()
                    .Where(oi => oi.OrderId == t_order.Id)
                    .Where(oi => oi.Status == EOrderState.Win)
                    .Sum(oi => oi.ActualPayout), 0),
                    // 状态判断：有Win则为Win，否则有Submitted则为Submitted，否则Lose
                    Status = SqlFunc.IIF(
                        SqlFunc.Subqueryable<Domain.OrderItem>().Where(oi => oi.OrderId == t_order.Id && oi.Status == EOrderState.Win).Any(),
                        EOrderState.Win,
                        EOrderState.Lose
                    )
                })
                // 可加Where条件限制只更新需要的订单
                .Where(o => o.RoundId == roundId && o.Status == EOrderState.Submitted)
                .ExecuteCommandAsync() > 0;
        }





        public async Task<Order> GetByIdAsync(Guid id)
        {
            var order = await dbContext.Orders.AsQueryable()
                .Includes(o => o.Items)
                .Where(o => o.Id == id)
                .Where(o => o.BranchId == currentUserService.BranchId)
                .FirstAsync();

            Throws.IfNullOrEmpty(order, "ticket is not find");

            var round = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.Id == order.RoundId)
                .FirstAsync();

            order.RoundResult = round.Result;

            return order;
        }

        private static object lockObj = new object();
        private static int seed = 0;

        public static string GenerateOrderNO()
        {
            lock (lockObj)
            {
                if (seed >= 1000)
                {
                    seed = 1;
                }
                else
                {
                    seed += new Random(Guid.NewGuid().GetHashCode()).Next(1, 9);
                }
                return DateTime.Now.ToString("yyMMddHHmm") + seed.ToString().PadLeft(3, '0');
            }
        }

        /// <summary>
        /// 分页查询订单列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PaginatedList<Order>> GetPaginatedListAsync(OrderQueryDto request)
        {
            //Order中的ActualPayout是通过Items中Status是否有Win计算出来的，不能直接在数据库中查询
            var orders = await dbContext.Orders.AsQueryable()
                .WhereIF(!string.IsNullOrEmpty(request.OrderNo), o => o.OrderNo.Contains(request.OrderNo))
                .WhereIF(request.MatchId.HasValue, o => o.MatchId == request.MatchId)
                .WhereIF(request.Round.HasValue, o => o.Round == request.Round)
                .WhereIF(request.BranchId.HasValue, o => o.BranchId == request.BranchId)
                .WhereIF(request.BranchIds?.Count > 0, o => request.BranchIds.Contains(o.BranchId))
                .WhereIF(request.CashierId.HasValue, o => o.CashierId == request.CashierId || o.SettledId == request.CashierId)
                .WhereIF(request.StartDate.HasValue, o => (o.CreateTime >= request.StartDate.Value) || (o.PayTime >= request.StartDate.Value))
                .WhereIF(request.EndDate.HasValue, o => (o.CreateTime <= request.EndDate.Value) || (o.PayTime <= request.EndDate.Value))
                .OrderByDescending(o => o.CreateTime)
                .PaginatedListAsync(request.PageIndex, request.PageSize);
            return orders;
        }

        public async Task<Order> GetByOrderNoAsync(string orderNo)
        {
            var order = await dbContext.Orders.AsQueryable()
           .Includes(o => o.Items)
           .Where(o => o.OrderNo == orderNo)
           .Where(o => o.BranchId == currentUserService.BranchId)
           .FirstAsync();

            Throws.IfNullOrEmpty(order, "ticket is not find");

            var round = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.Id == order.RoundId)
                .FirstAsync();

            order.RoundResult = round.Result;

            return order;
        }

        /// <summary>
        /// 结算订单
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public async Task<Order> CashAsync(Order order)
        {
            /**
             * 1. 先查询出订单
             * 2. 校验该订单是否属于该分店
             * 3. 校验该订单是否已经有结算结果
             * 4. 有结算结果则设置为已结算
             */
            var item = await dbContext.Orders.AsQueryable()
           .Where(o => o.Id == order.Id)
           .Where(o => o.BranchId == order.BranchId)
           .Where(o => o.Status == EOrderState.Win)
           .FirstAsync();

            Throws.IfNullOrEmpty(item, "order is not exist");

            order.Status = EOrderState.Paid;
            order.PayTime = DateTime.Now;
            order.SettledId = currentUserService.UserId;
            order.SettledBy = currentUserService.NickName;

            dbContext.Orders.AsUpdateable(order)
                .UpdateColumns(o => new { o.Status, o.PayTime, o.SettledId, o.SettledBy })
                .ExecuteCommand();

            return await Task.FromResult(order);
        }


        /// <summary>
        /// 取消订单
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public async Task<Order> CancelAsync(Order dto)
        {
            /**
             * 1. 先查询出订单
             * 2. 校验该订单是否属于该分店
             * 3. 校验该订单是否已经超出取消时间
             * 4. 取消订单
             */

            using (var uow = dbContext.CreateUnitOfWork(true))
            {

                var order = await uow.Db.Queryable<Order>()
                        .Where(o => o.Id == dto.Id)
                        .Where(o => o.BranchId == dto.BranchId)
                        .Where(o => o.Status == EOrderState.Submitted)
                        .Includes(o => o.Items)
                        .FirstAsync();

                Throws.IfNullOrEmpty(order, "order is not exist");

                if (DateTime.Now >= order.EndTime)
                {
                    throw new ValidateException("order is not allow cancel");
                }

                order.Status = EOrderState.Canceled;
                order.CancelTime = DateTime.Now;

                uow.Db.Updateable(order)
                    .UpdateColumns(o => new { o.Status, o.CancelTime })
                    .ExecuteCommand();


                uow.Db.Updateable<OrderItem>()
                    .Where(o => o.OrderId == order.Id)
                    .SetColumns(o => new OrderItem { Status = order.Status, CancelTime = order.CancelTime, UpdateTime = DateTime.Now })
                    .ExecuteCommand();

                //返还收银员金额
                uow.Db.Updateable<UserAssets>()
                    .Where(a => a.AccountId == order.CashierId && a.BranchId == order.BranchId)
                    .SetColumns(a => new UserAssets() { Balance = a.Balance + order.Stake })
                    .ExecuteCommand();

                uow.Commit();

                return await Task.FromResult(order);
            }
        }
    }
}
