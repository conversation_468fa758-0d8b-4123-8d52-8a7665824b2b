﻿using FreeScheduler;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.CoreService
{

    [Service(typeof(IScheduleTaskHandler))]
    public class ScheduleTaskHandler : IScheduleTaskHandler
    {
        [Autowired]
        protected readonly GenerateRoundProcessor generateRoundProcessor;
        [Autowired]
        protected readonly CarsRacingRoundProcessor carsRacingRoundProcessor;
        [Autowired]
        protected readonly LuckyRoulettenRoundProcessor luckyRoulettenRoundProcessor;
        [Autowired]
        protected readonly ColorLuckyRoundProcessor colorLuckyRoundProcessor;


        public async Task OnExecutingAsync(TaskInfo task)
        {
            switch (task.Topic)
            {
                //定时生成每日期数数据
                case EScheduleTopic.GenerateRoundTask:
                    await generateRoundProcessor.OnExecutingAsync(task);
                    break;
                //赛车结算
                case EScheduleTopic.CarsRacingRoundTask:
                    await carsRacingRoundProcessor.OnExecutingAsync(task);
                    break;
                //幸运轮盘结算
                case EScheduleTopic.LuckyRoulettenRoundTask:
                    await luckyRoulettenRoundProcessor.OnExecutingAsync(task);
                    break;
                //彩色球结算
                case EScheduleTopic.ColorLuckyRoundTask:
                    await colorLuckyRoundProcessor.OnExecutingAsync(task);
                    break;
            }
        }
    }
}
