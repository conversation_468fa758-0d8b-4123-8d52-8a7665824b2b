﻿using FreeRedis;
using NAutowired.Core.Models;
using System.Configuration;
using YStudio.Match.Domain;

namespace YStudio.Match.Repository
{
    [Repository]
    public class DbContext
    {
        [Autowired]
        public ISqlSugarClient Db { get; set; }

        public DbSet<Account> Accounts => new DbSet<Account>(Db);

        public DbSet<Order> Orders => new DbSet<Order>(Db);

        public DbSet<OrderItem> OrderItems => new DbSet<OrderItem>(Db);

        public DbSet<OrderItem> OrderSelections => new DbSet<OrderItem>(Db);

        public DbSet<Domain.Match> Matchs => new DbSet<Domain.Match>(Db);

        public DbSet<MatchOption> MatchOptions => new DbSet<MatchOption>(Db);

        public DbSet<Branch> Branchs => new DbSet<Branch>(Db);

        public DbSet<UserAssets> UserAssets => new DbSet<UserAssets>(Db);


        public DbSet<MatchRound> MatchRounds => new DbSet<MatchRound>(Db);

        public DbSet<MatchRoundLog> MatchRoundLogs => new DbSet<MatchRoundLog>(Db);


        public DbSet<OrderSnapshot> OrderSnapshots => new DbSet<OrderSnapshot>(Db);




        public DbSet<Config> Configs => new DbSet<Config>(Db);

        public DbSet<UserAssetsLog> UserAssetsLogs => new DbSet<UserAssetsLog>(Db);

        
        public SugarUnitOfWork CreateUnitOfWork(bool isTran = true)
        {
            return Db.CreateContext(isTran);
        }

    }
}
