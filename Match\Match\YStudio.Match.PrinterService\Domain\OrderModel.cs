﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.PrinterService.Domain
{
    public class OrderModel
    {

        public string Title { get; set; }
        public string BranchName { get; set; }

        public string Cashier { get; set; } = string.Empty;

        public string OrderNo { get; set; } = string.Empty;

        public long Round { get; set; }

        public string MatchName { get; set; }

        public decimal? MaxPayout { get; set; } = 0;

        public decimal? ActualPayout { get; set; } = 0;

        public decimal? Stake { get; set; } = 0;

        public List<OrderItemModel> Items { get; set; }

        public DateTime CreateTime { get; set; }
    }
    public class OrderItemModel
    {

        public decimal Stake { get; set; }

        public decimal MaxPayout { get; set; }

        public decimal Odds { get; set; }

        public string OptionName { get; set; }

        public string OptionType { get; set; }

        public int Status { get; set; }

    }
}
