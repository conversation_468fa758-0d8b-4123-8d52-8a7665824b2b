﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.CarsRacing
{
    public class CarsRacingHandler
    {
        public static IList<CarNumber> Numbers { get; set; } = new List<CarNumber>();

        public static IList<CarOption> Options { get; set; } = new List<CarOption>();

        /// <summary>
        /// 第一名第二名所有的组合 包括每个组合中有哪些下单选项
        /// </summary>
        public static IList<CarCombination> Combinations { get; set; } = new List<CarCombination>();

        public StringBuilder LogMessage = new StringBuilder();

        /// <summary>
        /// 随机数生成器
        /// </summary>
        private static Random random = new(Guid.NewGuid().GetHashCode());

        static CarsRacingHandler()
        {
            Numbers.Add(new(1, false, ESize.Small));
            Numbers.Add(new(2, true, ESize.Small));
            Numbers.Add(new(3, false, ESize.Small));
            Numbers.Add(new(4, true, ESize.Big));
            Numbers.Add(new(5, false, ESize.Big));
            Numbers.Add(new(6, true, ESize.Big));

            //第一名
            Options.Add(new(2001, "1", 3.5m, EOptionType.Win, new List<int> { 1 }));
            Options.Add(new(2002, "2", 4.5m, EOptionType.Win, new List<int> { 2 }));
            Options.Add(new(2003, "3", 5.0m, EOptionType.Win, new List<int> { 3 }));
            Options.Add(new(2004, "4", 6.0m, EOptionType.Win, new List<int> { 4 }));
            Options.Add(new(2005, "5", 6.0m, EOptionType.Win, new List<int> { 5 }));
            Options.Add(new(2006, "6", 7.2m, EOptionType.Win, new List<int> { 6 }));

            //第一名或第二名
            Options.Add(new(2007, "1", 1.85m, EOptionType.FirstOrSecond, new List<int> { 1 }));
            Options.Add(new(2008, "2", 2.30m, EOptionType.FirstOrSecond, new List<int> { 2 }));
            Options.Add(new(2009, "3", 2.50m, EOptionType.FirstOrSecond, new List<int> { 3 }));
            Options.Add(new(2010, "4", 3.00m, EOptionType.FirstOrSecond, new List<int> { 4 }));
            Options.Add(new(2011, "5", 3.00m, EOptionType.FirstOrSecond, new List<int> { 5 }));
            Options.Add(new(2012, "6", 3.50m, EOptionType.FirstOrSecond, new List<int> { 6 }));

            //大小单双
            Options.Add(new(2013, "Low", 1.50m, EOptionType.Size, new List<int> { 1, 2, 3 }));
            Options.Add(new(2014, "High", 2.20m, EOptionType.Size, new List<int> { 4, 5, 6 }));
            Options.Add(new(2015, "Even", 2.00m, EOptionType.Even, new List<int> { 2, 4, 6 }));
            Options.Add(new(2016, "Odd", 1.60m, EOptionType.Even, new List<int> { 1, 3, 5 }));

            //预测 第一二名的组合
            Options.Add(new(2017, "1x2", 13.00m, EOptionType.Forecast, new List<int> { 1, 2 }));
            Options.Add(new(2018, "1x3", 15.00m, EOptionType.Forecast, new List<int> { 1, 3 }));
            Options.Add(new(2019, "1x4", 18.00m, EOptionType.Forecast, new List<int> { 1, 4 }));
            Options.Add(new(2020, "1x5", 18.00m, EOptionType.Forecast, new List<int> { 1, 5 }));
            Options.Add(new(2021, "1x6", 22.00m, EOptionType.Forecast, new List<int> { 1, 6 }));
            Options.Add(new(2022, "2x1", 14.00m, EOptionType.Forecast, new List<int> { 2, 1 }));
            Options.Add(new(2023, "2x3", 20.00m, EOptionType.Forecast, new List<int> { 2, 3 }));
            Options.Add(new(2024, "2x4", 23.00m, EOptionType.Forecast, new List<int> { 2, 4 }));
            Options.Add(new(2025, "2x5", 24.00m, EOptionType.Forecast, new List<int> { 2, 5 }));
            Options.Add(new(2026, "2x6", 29.00m, EOptionType.Forecast, new List<int> { 2, 6 }));
            Options.Add(new(2027, "3x1", 16.00m, EOptionType.Forecast, new List<int> { 3, 1 }));
            Options.Add(new(2028, "3x2", 21.00m, EOptionType.Forecast, new List<int> { 3, 2 }));
            Options.Add(new(2029, "3x4", 27.00m, EOptionType.Forecast, new List<int> { 3, 4 }));
            Options.Add(new(2030, "3x5", 27.00m, EOptionType.Forecast, new List<int> { 3, 5 }));
            Options.Add(new(2031, "3x6", 32.00m, EOptionType.Forecast, new List<int> { 3, 6 }));
            Options.Add(new(2032, "4x1", 20.00m, EOptionType.Forecast, new List<int> { 4, 1 }));
            Options.Add(new(2033, "4x2", 26.00m, EOptionType.Forecast, new List<int> { 4, 2 }));
            Options.Add(new(2034, "4x3", 28.00m, EOptionType.Forecast, new List<int> { 4, 3 }));
            Options.Add(new(2035, "4x5", 33.00m, EOptionType.Forecast, new List<int> { 4, 5 }));
            Options.Add(new(2036, "4x6", 39.00m, EOptionType.Forecast, new List<int> { 4, 6 }));
            Options.Add(new(2037, "5x1", 20.00m, EOptionType.Forecast, new List<int> { 5, 1 }));
            Options.Add(new(2038, "5x2", 26.00m, EOptionType.Forecast, new List<int> { 5, 2 }));
            Options.Add(new(2039, "5x3", 28.00m, EOptionType.Forecast, new List<int> { 5, 3 }));
            Options.Add(new(2040, "5x4", 33.00m, EOptionType.Forecast, new List<int> { 5, 4 }));
            Options.Add(new(2041, "5x6", 39.00m, EOptionType.Forecast, new List<int> { 5, 6 }));
            Options.Add(new(2042, "6x1", 25.00m, EOptionType.Forecast, new List<int> { 6, 1 }));
            Options.Add(new(2043, "6x2", 31.00m, EOptionType.Forecast, new List<int> { 6, 2 }));
            Options.Add(new(2044, "6x3", 35.00m, EOptionType.Forecast, new List<int> { 6, 3 }));
            Options.Add(new(2045, "6x4", 40.00m, EOptionType.Forecast, new List<int> { 6, 4 }));
            Options.Add(new(2046, "6x5", 42.00m, EOptionType.Forecast, new List<int> { 6, 5 }));

            //生成30种组合 以及每个组合中包含的OptionId
            for (int first = 1; first <= 6; first++)
            {
                for (int second = 1; second <= 6; second++)
                {
                    if (first == second)
                        continue;

                    var combination = new CarCombination(first, second);

                    //第一名
                    var win = Options.Where(o => o.Type == EOptionType.Win)
                        .Where(o => o.Numbers[0] == first)
                        .Select(o => o.OptionId)
                        .ToList();
                    combination.Options.AddRange(win);


                    //第一名或者第二名
                    var firstOrSecond = Options.Where(o => o.Type == EOptionType.FirstOrSecond)
                        .Where(o => o.Numbers[0] == first || o.Numbers[0] == second)
                        .Select(o => o.OptionId)
                        .ToList();
                    combination.Options.AddRange(firstOrSecond);


                    //大小单双
                    var other = Options.Where(o => o.Type == EOptionType.Size || o.Type == EOptionType.Even)
                        .Where(o => o.Numbers.Contains(first))
                        .Select(o => o.OptionId)
                        .ToList();
                    combination.Options.AddRange(other);


                    //预测
                    var forecast = Options.Where(o => o.Type == EOptionType.Forecast)
                      .Where(o => o.Numbers[0] == first && o.Numbers[1] == second)
                      .Select(o => o.OptionId)
                      .ToList();
                    combination.Options.AddRange(forecast);


                    Combinations.Add(combination);
                }
            }
            //Combinations.Add(new(1, 2, new List<int> { 2001, 2007, 2008, 2013, 2016, 2017 }));
        }


        public List<int> Handle(Dictionary<string, decimal> stakes, decimal reserveRatio = 0.20m, int minPayoutRate = 5)
        {

            //每个选项的订单金额
            //stakes = new()
            //{
            //    {"2046_A", 500 },
            //    {"2044_B", 500 },
            //    {"2042_C", 500 },
            //    {"2040_D", 500 },
            //    {"2045_F", 500 },
            //    { "2001_1", 1000}
            //};

            //累计投注金额
            decimal totalStake = stakes.Values.Sum();

            LogMessage.AppendLine($"累计下单金额: {totalStake}");


            //预计赔付金额
            var estPayout = totalStake * (1 - reserveRatio);

            //每个OptionId 下单的金额统计
            ConcurrentDictionary<int, decimal> newStakes = new ConcurrentDictionary<int, decimal>();

            var keys = stakes.Keys.ToList();

            foreach (var key in keys)
            {
                //如果key不符合格式，跳过
                int? optionId = int.Parse(key.Split("_")[0]);
                if (!optionId.HasValue)
                    continue;

                //下单的Option
                var option = Options.FirstOrDefault(x => x.OptionId == optionId.Value);
                if (option == null)
                    continue;

                //每个选项的下单金额
                newStakes.AddOrUpdate(optionId.Value, stakes[key], (k, v) => v + stakes[key]);
            }


            //每种组合下单的总金额
            ConcurrentDictionary<string, decimal> combinationStake = new ConcurrentDictionary<string, decimal>();
            //每种组合赔付的总金额
            ConcurrentDictionary<string, decimal> combinationPayout = new ConcurrentDictionary<string, decimal>();

            foreach (var item in Combinations)
            {
                decimal payout = 0;
                decimal combinationTotalStake = 0;

                //组合下所有的选项
                foreach (var optionId in item.Options)
                {
                    decimal stake = newStakes.GetValueOrDefault(optionId, 0);
                    if (stake == 0)
                        continue;

                    //选项信息 含赔率
                    var option = Options.Where(o => o.OptionId == optionId).First();

                    //累计该组合的总赔付金额
                    payout += stake * option.Odds;
                    //累计该组合的总下单金额
                    combinationTotalStake += stake;
                }

                //存储所有组合的下单金额表
                combinationStake.TryAdd($"{item.FirstNumber}_{item.SecondNumber}", combinationTotalStake);

                //存储所有组合的赔付金额表
                combinationPayout.TryAdd($"{item.FirstNumber}_{item.SecondNumber}", payout);
                //combinationPayout.AddOrUpdate($"{item.FirstNumber}_{item.SecondNumber}", payout, (k, v) => v + payout);
            }


            //输出每个数字的订单金额 和赔付金额
            combinationStake.OrderBy(x => x.Key).ToList().ForEach(x =>
            {
                LogMessage.AppendLine($"组合 {x.Key} 订单金额: {x.Value} 赔付金额: {combinationPayout[x.Key]}");
            });


            LogMessage.AppendLine($"吃分比例: {reserveRatio * 100} %");
            LogMessage.AppendLine($"预计赔付金额: {estPayout}");

            var numberKey = GetCombinationKey(combinationPayout, estPayout, minPayoutRate);

            var numbers = numberKey.Split("_").Select(x => int.Parse(x)).ToList();

            numbers.AddRange(Shuffle(Numbers.Where(n => !numbers.Contains(n.Number)).Select(n => n.Number).ToList()));

            LogMessage.AppendLine($"结算组合结果: {string.Join(",", numbers)}");

            return numbers;
        }

        public IList<T> Shuffle<T>(IList<T> list)
        {
            Random rng = new Random();
            int n = list.Count;
            while (n > 1)
            {
                n--;
                int k = rng.Next(n + 1);
                T value = list[k];
                list[k] = list[n];
                list[n] = value;
            }
            return list;
        }

        private string GetCombinationKey(ConcurrentDictionary<string, decimal> combinationPayout, decimal estPayout, int minpayoutRate)
        {

            //在等于赔付金额的数字列表随机取一个
            var equalList = combinationPayout.Where(x => x.Value == estPayout).Where(x => x.Value > 0).ToList();
            if (equalList.Count > 0)
            {
                int index = random.Next(0, equalList.Count);
                LogMessage.AppendLine($"符合吃分规则1,在等于吃分赔付金额规则的{equalList.Count}个组合中选择,结算组合为 {equalList[index].Key}");
                return equalList[index].Key;
            }

            //在小于赔付金额的数字列表取前3个中随机一个
            var lessList = combinationPayout.Where(x => x.Value < estPayout).Where(x => x.Value > 0).OrderBy(x => x.Value).Take(10).ToList();
            if (lessList.Count > 0)
            {
                int index = random.Next(0, lessList.Count);
                LogMessage.AppendLine($"符合吃分规则2,在小于吃分赔付金额规则的{lessList.Count}个组合中选择,结算组合为 {lessList[index].Key}");
                return lessList[index].Key;
            }


            int randomRate = random.Next(1, 101);
            LogMessage.AppendLine($"通吃比例配置为 {minpayoutRate} % ,本次随机概率为 {randomRate} %");

            //在通吃概率范围内
            if (randomRate <= minpayoutRate)
            {
                //1.优先开不用赔付的组合
                //2.没有这个组合的话，开赔付最少的组合
                var zeroPayoutList = combinationPayout.Where(x => x.Value == 0).ToList();
                if (zeroPayoutList.Count > 0)
                {
                    int index = random.Next(0, zeroPayoutList.Count);
                    LogMessage.AppendLine($"符合通吃规则,0赔付,结算组合为 {zeroPayoutList[index].Key}");
                    return zeroPayoutList[index].Key;
                }
                else
                {
                    var minPayout = combinationPayout.OrderBy(x => x.Value).First();
                    //最小赔付金额可能有多个组合 需要随机取一个
                    if (combinationPayout.Count(x => x.Value == minPayout.Value) > 1)
                    {
                        var minPayoutList = combinationPayout.Where(x => x.Value == minPayout.Value).ToList();
                        int index = random.Next(0, minPayoutList.Count);
                        minPayout = minPayoutList[index];
                    }
                    LogMessage.AppendLine($"符合最低赔付规则,赔付 {minPayout} ,结算组合为 {minPayout.Key}");
                    return minPayout.Key;
                }
            }

            ////在大于赔付金额的数字列表取第一个
            //var greaterList = combinationPayout.Where(x => x.Value > estPayout).OrderBy(x => x.Value).ToList();
            //if (greaterList.Count > 0)
            //{
            //    return greaterList[0].Key;
            //}

            //随机取1 2 3 4 5 6 里面的2个数字 但是只能取一次 并且每个数字有不同的权重配置
            var key = GetRandomNumbersByWeight(2).Select(x => x.ToString()).Aggregate((a, b) => $"{a}_{b}");
            LogMessage.AppendLine($"不符合吃分规则,未进入通吃规则,根据[权重]结算组合为 {key}");
            return key;
        }


        public IList<int> GetRandomNumbersByWeight(int count = 2)
        {
            var result = new List<int>();
            var availableOptions = Options.Where(o => o.Type == EOptionType.Win).ToList();
            for (int i = 0; i < count; i++)
            {
                // 计算权重倒数
                var weights = availableOptions.Select(o => 1000m / o.Odds).ToList();
                var totalWeight = weights.Sum();

                // 累积概率分布
                var cumulative = new List<decimal>();
                decimal sum = 0;
                foreach (var w in weights)
                {
                    sum += w;
                    cumulative.Add(sum);
                }

                // 随机抽取
                var value = (decimal)random.NextDouble() * totalWeight;
                int selectedIndex = 0;
                for (; selectedIndex < cumulative.Count; selectedIndex++)
                {
                    if (value < cumulative[selectedIndex])
                        break;
                }

                // 添加结果并移除已选项
                result.Add(availableOptions[selectedIndex].Numbers[0]);
                availableOptions.RemoveAt(selectedIndex);
            }

            return result;
        }


        /// <summary>
        /// 根据第一名和第二名的数字获取对应的选项Id列表
        /// </summary>
        /// <param name="numbers"></param>
        /// <returns></returns>
        public IList<int> GetOptionsByNumbers(IList<int> numbers)
        {
            if (numbers == null || numbers.Count == 0)
                return new List<int>();
            //获取所有组合中包含的OptionId
            var options = Combinations.Where(c => c.FirstNumber == numbers[0] && c.SecondNumber == numbers[1])
                .SelectMany(c => c.Options)
                .Distinct()
                .ToList();
            return options;
        }
    }
}
