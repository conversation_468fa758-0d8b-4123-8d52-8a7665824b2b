﻿using FreeRedis;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.Authorization;

namespace YStudio.Infrastructure.Extensions
{
    public static partial class Extensions
    {
        public static RedisClient AddFreeRedis(this IServiceCollection services)
        {
            var connectionString = services.BuildServiceProvider()
                .GetRequiredService<IConfiguration>()
                .GetSection("Redis")
                .Get<string>();

            RedisClient cli = new RedisClient(connectionString);
            cli.Serialize = obj => JsonConvert.SerializeObject(obj);
            cli.Deserialize = (json, type) => JsonConvert.DeserializeObject(json, type);

            //cli.Notice += (s, e) => Console.WriteLine(e.Log);

            services.AddSingleton(cli);
            return cli;
        }
    }
}
