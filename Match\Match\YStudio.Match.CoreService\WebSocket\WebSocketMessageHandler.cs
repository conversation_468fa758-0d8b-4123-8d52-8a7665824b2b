﻿using NAutowired.Core.Models;
using NetCoreServer;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;
using YStudio.Match.CoreService.WebSocket.Processor;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.CoreService
{
    [Component(Lifetime.Transient)]
    public class WebSocketMessageHandler : IMessageHandler
    {

        [Autowired]
        private readonly WebSocketSessionManager _sessionManager;

        [Autowired]
        private readonly DbContext dbContext;

        [Autowired]
        private readonly PingCommandProcessor pingCommandProcessor;

        [Autowired]
        private readonly LuckyRoulettenRecentProcessor luckyRoulettenRecentProcessor;

        [Autowired]
        private readonly ColorLuckyRecentProcessor colorLuckyRecentProcessor;

        [Autowired]
        private readonly CarsRacingRecentProcessor carsRacingRecentProcessor;

        public async Task HandleMessageAsync(WebSocketSession session, string message)
        {
            var json = JsonUtils.Deserialize<JToken>(message);
            string command = json.Value<string>("command") ?? throw new ArgumentNullException("command", "Command cannot be null");
            switch (command)
            {
                case ECommand.Ping:
                    await pingCommandProcessor.ProcessAsync(session, JsonUtils.Deserialize<PingData>(message));
                    break;
                default:
                    session.Close(-2);
                    break;
            }
        }

        public async Task OnConnectedAsync(WebSocketSession session, HttpRequest request)
        {
            var query = StringUtils.ParseQueryString(request.Url);
            query.TryGetValue("match", out string? match);

            if (string.IsNullOrEmpty(match))
            {
                session.Close(-2);
                return;
            }

            _sessionManager.AddGroup(match, session);

            switch (match)
            {
                case "1000":
                    await luckyRoulettenRecentProcessor.ProcessAsync(session, match);
                    break;
                case "2000":
                    await carsRacingRecentProcessor.ProcessAsync(session, match);
                    break;
                case "3000":
                    await colorLuckyRecentProcessor.ProcessAsync(session, match);
                    break;
                default:
                    session.Close(-2);
                    break;
            }
            return;
        }
    }
}
