﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.CoreService
{
    [Service]
    public class UserService
    {
        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly ICurrentUserService currentUserService;


        public async Task<UserAssets> GetUserAssetsAsync(int userId)
        {
            var assets = await dbContext.UserAssets.AsQueryable()
                .Where(x => x.AccountId == userId)
                .Where(x => x.BranchId == currentUserService.BranchId)
                .FirstAsync();
            Throws.IfNullOrEmpty(assets, "User assets not found");
            return assets;
        }
    }
}
