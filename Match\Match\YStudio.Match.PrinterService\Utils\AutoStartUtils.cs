﻿using Microsoft.Win32;
using System.Windows.Forms;

namespace YStudio.Match.PrinterService.Utils
{
    public class AutoStartUtils
    {
        private const string RunKey = @"Software\Microsoft\Windows\CurrentVersion\Run";
        private static string AppName => Application.ProductName;

        public static void SetAutoStart(bool enable)
        {
            using (RegistryKey key = Registry.CurrentUser.OpenSubKey(RunKey, true))
            {
                if (enable)
                {
                    string exePath = Application.ExecutablePath;
                    key.SetValue(AppName, $"\"{exePath}\"");
                }
                else
                {
                    key.DeleteValue(AppName, false);
                }
            }
        }

        public static bool IsAutoStartEnabled()
        {
            using (RegistryKey key = Registry.CurrentUser.OpenSubKey(RunKey, false))
            {
                var value = key.GetValue(AppName);
                return value != null;
            }
        }
    }
}
