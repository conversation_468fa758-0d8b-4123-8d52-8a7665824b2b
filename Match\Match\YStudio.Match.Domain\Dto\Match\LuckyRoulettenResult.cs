﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    public class LuckyRoulettenResult
    {
        public int MatchId { get; set; }
        public string Command { get; set; } = string.Empty;
        public int? CurrentRound { get; set; }
        public long CurrentTime { get; set; }
        public int NextRound { get; set; }
        public long NextTime { get; set; }
        public object? CurrentNumber { get; set; }
        public List<LuckyRoulettenRoundResult> RecentResult { get; set; } = new List<LuckyRoulettenRoundResult>();
    }

    public class LuckyRoulettenRoundResult
    {
        public int Round { get; set; }
        public long RoundTime { get; set; }

        public object Result { get; set; } = new List<int>();
    }
}
