﻿using FreeScheduler;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static FreeSql.Internal.GlobalFilter;

namespace YStudio.Match.CoreService
{

    [Component]
    public class GenerateRoundProcessor : IScheduleTaskHandler
    {
        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly Scheduler scheduler;

        [Autowired]
        protected readonly ILogger<GenerateRoundProcessor> logger;

        /// <summary>
        /// 定时期数数据生成
        /// </summary>
        /// <param name="task"></param>
        /// <returns></returns>
        public async Task OnExecutingAsync(TaskInfo task)
        {

            using (await AsyncLocker.GenerateRoundLocker.LockAsync())
            {
                try
                {
                    await GenerateRoundsAsync();
                }
                catch (Exception ex)
                {
                    // 记录异常日志
                    logger.LogError(ex, "Error occurred while generating match rounds.");
                }
            }
        }

        private async Task GenerateRoundsAsync()
        {
            var matchs = await dbContext.Matchs.AsQueryable()
                .Where(m => m.Status == ENormalStatus.Normal)
                .ToListAsync();

            var now = DateTime.Now;
            DateOnly currentDate = DateOnly.FromDateTime(now);

            // 当天的23:00:00才开始生成明天的期数
            var startGenerateNextDayRounds = now >= now.Date.AddHours(23);

            foreach (var match in matchs)
            {
                // 1. ScheduleDate为null，生成当天期数
                // 2. ScheduleDate小于当天，生成当天期数
                if (match.ScheduleDate == null || match.ScheduleDate < currentDate)
                {
                    await GenerateRoundsForDate(match, currentDate);
                }
                // 3. ScheduleDate为今天 并且是当天23点后，生成明天期数 
                else if (currentDate == match.ScheduleDate && startGenerateNextDayRounds)
                {
                    await GenerateRoundsForDate(match, currentDate.AddDays(1));
                }
                // 其他情况不处理
            }
        }
        /// <summary>
        /// 生成指定日期的期数
        /// </summary>
        private async Task GenerateRoundsForDate(Domain.Match match, DateOnly date)
        {
            DateTime baseDate = date.ToDateTime(TimeOnly.MinValue);
            DateTime startTime = baseDate.Add(match.StartTime);

            // 计算理论上的结束时间
            DateTime endTime = baseDate.AddDays(match.EndTime < match.StartTime ? 1 : 0).Add(match.EndTime);

            // 当天的最后一秒
            DateTime dayEnd = baseDate.AddDays(1).AddSeconds(-1);

            int round = 1;
            var rounds = new List<MatchRound>();
            var now = DateTime.Now;
            while (startTime < endTime && startTime < dayEnd)
            {
                // 如果开始时间已过当前时间，则跳过本轮
                if (startTime < now)
                {
                    startTime = startTime.AddSeconds(Convert.ToDouble(match.Interval ?? 0));
                    continue;
                }

                // 计算本期的结束时间
                DateTime roundEndTime = startTime.AddSeconds(Convert.ToDouble(match.Interval ?? 0));

                // 如果本期结束时间超过当天最后一秒，终止循环
                if (roundEndTime > dayEnd)
                {
                    roundEndTime = dayEnd;
                    break;
                }

                var item = new MatchRound
                {
                    Id = Guid.NewGuid(),
                    MatchId = match.Id,
                    Round = round,
                    StartTime = startTime,
                    Interval = match.Interval,
                    EndTime = roundEndTime,
                    CreateTime = DateTime.Now
                };
                rounds.Add(item);
                round++;
                startTime = roundEndTime;

                // 如果已经到达当天最后一秒，终止循环
                if (roundEndTime >= dayEnd)
                    break;
            }
            using (var uow = dbContext.CreateUnitOfWork())
            {
                await dbContext.MatchRounds.InsertRangeAsync(rounds);
                match.ScheduleDate = date;
                await dbContext.Matchs.AsUpdateable(match).UpdateColumns(m => m.ScheduleDate).ExecuteCommandAsync();
                uow.Commit();
            }

            await BatchAddTaskAsync(rounds);
        }

        /// <summary>
        /// 批量添加定时任务
        /// </summary>
        /// <param name="rounds"></param>
        /// <exception cref="NotImplementedException"></exception>
        private async Task BatchAddTaskAsync(List<MatchRound> rounds)
        {
            await Parallel.ForEachAsync(rounds, async (item, token) =>
            {
                //如果当前时间已经过了结束时间，则不添加任务
                if (item.EndTime > DateTime.Now)
                {
                    //添加定时任务
                    item.TaskId = AddTask(item);
                }
            });

            var newRounds = rounds.Where(item => !string.IsNullOrEmpty(item.TaskId))
                 .ToList();

            await dbContext.MatchRounds.AsUpdateable(newRounds)
                          .UpdateColumns(it => new { it.TaskId })
                          .ExecuteCommandAsync();

        }

        /// <summary>
        /// 添加单个游戏期数的定时任务
        /// </summary>
        /// <param name="round"></param>
        /// <param name="replace"></param>
        /// <returns></returns>
        private string? AddTask(MatchRound round, bool replace = true)
        {
            var topics = new Dictionary<int, string> {
                {EMatch.LuckyRouletten, EScheduleTopic.LuckyRoulettenRoundTask },
                {EMatch.CarsRacing, EScheduleTopic.CarsRacingRoundTask },
                {EMatch.ColorLucky, EScheduleTopic.ColorLuckyRoundTask }
            };

            //游戏Id对应的定时任务Topic
            topics.TryGetValue(round.MatchId, out string? topic);

            //未配置 直接剔除
            if (string.IsNullOrEmpty(topic))
            {
                return null;
            }

            string execTime = round.EndTime.ToString("HH:mm:ss");
            string body = $"{round.MatchId}_{round.EndTime.ToString("yyyyMMdd")}_{round.Round}_{round.Id}";

            var task = scheduler.FindTask(t => t.Topic == topic && t.Body == body);

            //无原任务 直接添加返回
            if (task.Length == 0)
            {
                return scheduler.AddTaskRunOnMonth(topic, body, 1, $"{round.EndTime.Day}:{execTime}");
            }

            //存在任务 删除原任务
            if (replace && task.Length > 0)
            {
                task.ToList().ForEach(item =>
                {
                    scheduler.RemoveTask(item.Id);
                });
            }

            return scheduler.AddTaskRunOnDay(topic, body, 1, execTime);
        }
    }
}