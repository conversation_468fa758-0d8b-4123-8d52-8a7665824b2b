﻿using NetCoreServer;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure.WebSocket
{
    public class WebSocketSessionManager
    {
        private readonly ConcurrentDictionary<Guid, WebSocketSession> _sessions = new();

        private readonly ConcurrentDictionary<string, ConcurrentDictionary<Guid, WebSocketSession>> _groups = new();
        public void Add(WebSocketSession session, HttpRequest request)
        {
            if (session == null) throw new ArgumentNullException(nameof(session));
            _sessions[session.Id] = session;

            var query = StringUtils.ParseQueryString(request.Url);
            query.TryGetValue("match", out string? group);

            if (string.IsNullOrEmpty(group))
            {
                session.Close(-1);
                return;
            }

            AddGroup(group, session);
        }


        public void Remove(Guid sessionId)
        {
            _sessions.TryRemove(sessionId, out _);
            foreach (var group in _groups.Values)
            {
                group.TryRemove(sessionId, out _);
            }
        }

        public void AddGroup(string groupName, WebSocketSession session)
        {
            var group = _groups.GetOrAdd(groupName, _ => new ConcurrentDictionary<Guid, WebSocketSession>());
            group[session.Id] = session;
        }

        public void RemoveGroup(string groupName, Guid sessionId)
        {
            if (_groups.TryGetValue(groupName, out var group))
            {
                group.TryRemove(sessionId, out _);
            }
        }

        public IEnumerable<WebSocketSession> GetGroupSessions(string groupName)
        {
            if (_groups.TryGetValue(groupName, out var group))
                return group.Values;
            return Enumerable.Empty<WebSocketSession>();
        }

        public void BroadcastToGroup(string groupName, string message)
        {
            foreach (var session in GetGroupSessions(groupName))
            {
                session.SendTextAsync(message);
            }
        }

        public WebSocketSession? GetSession(Guid sessionId)
        {
            _sessions.TryGetValue(sessionId, out var session);
            return session;
        }


    }
}
