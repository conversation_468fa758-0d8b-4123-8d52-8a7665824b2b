﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Match.Domain;
using YStudio.Match.Domain.Vo.Report;

namespace YStudio.Match.CoreService
{

    [Service]
    public class ReportService
    {
        [Autowired]
        protected readonly DbContext dbContext;


        public IList<MatchReportVo> Query(ReportQueryDto reportQueryDto)
        {
            /**
                营业额 = 当天下单总金额
                赔付额 = 当天开奖赔付额（未兑奖+）已兑奖
                已兑奖 = 当天兑奖金额（比如昨天的票今天兑也会统计在今天）
                未兑奖 = 当天未兑奖金额
                盈利 = 营业额 - 赔付额
                盈利比率 = 盈利 / 营业额
            */

            List<MatchReportVo> list = new List<MatchReportVo>();

            if (reportQueryDto.AgentId.HasValue && !reportQueryDto.BranchId.HasValue)
            {
                //如果传了代理商ID，但没传营业网点ID，则查询该代理商下所有营业网点
                reportQueryDto.BranchIds = dbContext.Branchs.AsQueryable()
                    .Where(b => b.AccountId == reportQueryDto.AgentId)
                    .Select(b => b.Id).ToList();
            }

            if (reportQueryDto.BranchId.HasValue)
            {
                //如果传了营业网点ID，则忽略代理商ID
                reportQueryDto.BranchIds = null;
            }

            if (reportQueryDto.EndDate.HasValue)
            {
                reportQueryDto.EndDate = reportQueryDto.EndDate.Value.AddDays(1).AddSeconds(-1);
            }

            if (reportQueryDto.MatchId.HasValue)
            {
                list.Add(QueryItem(reportQueryDto));
            }
            else
            {
                reportQueryDto.MatchId = EMatch.LuckyRouletten;
                list.Add(QueryItem(reportQueryDto));
                reportQueryDto.MatchId = EMatch.CarsRacing;
                list.Add(QueryItem(reportQueryDto));
                reportQueryDto.MatchId = EMatch.ColorLucky;
                list.Add(QueryItem(reportQueryDto));
            }

            //为list 增加总计
            list.Add(new MatchReportVo
            {
                MatchId = 0,
                MatchName = "总计",
                TotalStake = list.Sum(x => x.TotalStake),
                TotalPayout = list.Sum(x => x.TotalPayout),
                TotalPaid = list.Sum(x => x.TotalPaid),
                TotalUnPaid = list.Sum(x => x.TotalUnPaid),
                Profit = list.Sum(x => x.TotalStake) - list.Sum(x => x.TotalPayout),
                ProfitRate = list.Sum(x => x.TotalStake) == 0 ? 0 : (list.Sum(x => x.TotalStake) - list.Sum(x => x.TotalPayout)) / list.Sum(x => x.TotalStake)
            });

            return list;
        }

        private MatchReportVo QueryItem(ReportQueryDto reportQueryDto)
        {
            //营业额
            var totalStake = dbContext.Orders.AsQueryable()
                .Where(o => o.Status != EOrderState.Canceled)
                .WhereIF(reportQueryDto.BranchId.HasValue, o => o.BranchId == reportQueryDto.BranchId)
                .WhereIF(reportQueryDto.BranchIds?.Count > 0, o => reportQueryDto.BranchIds.Contains(o.BranchId))
                .WhereIF(reportQueryDto.CashierId.HasValue, o => o.CashierId == reportQueryDto.CashierId)
                .WhereIF(reportQueryDto.MatchId.HasValue, o => o.MatchId == reportQueryDto.MatchId)
                .WhereIF(reportQueryDto.StartDate.HasValue, o => o.CreateTime >= reportQueryDto.StartDate)
                .WhereIF(reportQueryDto.EndDate.HasValue, o => o.CreateTime <= reportQueryDto.EndDate)
                .Sum(o => o.Stake);


            //赔付额
            var totalPayout = dbContext.Orders.AsQueryable()
                .Where(o => o.Status == EOrderState.Win || o.Status == EOrderState.Paid)
                .WhereIF(reportQueryDto.BranchId.HasValue, o => o.BranchId == reportQueryDto.BranchId)
                .WhereIF(reportQueryDto.BranchIds?.Count > 0, o => reportQueryDto.BranchIds.Contains(o.BranchId))
                .WhereIF(reportQueryDto.CashierId.HasValue, o => o.CashierId == reportQueryDto.CashierId)
                .WhereIF(reportQueryDto.MatchId.HasValue, o => o.MatchId == reportQueryDto.MatchId)
                .WhereIF(reportQueryDto.StartDate.HasValue, o => o.CreateTime >= reportQueryDto.StartDate)
                .WhereIF(reportQueryDto.EndDate.HasValue, o => o.CreateTime <= reportQueryDto.EndDate)
                .Sum(o => o.ActualPayout);



            //已兑奖
            var totalPaid = dbContext.Orders.AsQueryable()
                .Where(o => o.Status == EOrderState.Paid)
                .WhereIF(reportQueryDto.BranchId.HasValue, o => o.BranchId == reportQueryDto.BranchId)
                .WhereIF(reportQueryDto.BranchIds?.Count > 0, o => reportQueryDto.BranchIds.Contains(o.BranchId))
                .WhereIF(reportQueryDto.CashierId.HasValue, o => o.SettledId == reportQueryDto.CashierId)
                .WhereIF(reportQueryDto.MatchId.HasValue, o => o.MatchId == reportQueryDto.MatchId)
                .WhereIF(reportQueryDto.StartDate.HasValue, o => o.PayTime >= reportQueryDto.StartDate)
                .WhereIF(reportQueryDto.EndDate.HasValue, o => o.PayTime <= reportQueryDto.EndDate)
                .Sum(o => o.ActualPayout);

            //未兑奖
            var totalUnPaid = dbContext.Orders.AsQueryable()
                .Where(o => o.Status == EOrderState.Win)
                .WhereIF(reportQueryDto.BranchId.HasValue, o => o.BranchId == reportQueryDto.BranchId)
                .WhereIF(reportQueryDto.BranchIds?.Count > 0, o => reportQueryDto.BranchIds.Contains(o.BranchId))
                .WhereIF(reportQueryDto.CashierId.HasValue, o => o.CashierId == reportQueryDto.CashierId)
                .WhereIF(reportQueryDto.MatchId.HasValue, o => o.MatchId == reportQueryDto.MatchId)
                .WhereIF(reportQueryDto.StartDate.HasValue, o => o.CreateTime >= reportQueryDto.StartDate)
                .WhereIF(reportQueryDto.EndDate.HasValue, o => o.CreateTime <= reportQueryDto.EndDate)
                .Sum(o => o.ActualPayout);

            return new MatchReportVo
            {
                MatchId = reportQueryDto.MatchId.Value,
                MatchName = dbContext.Matchs.AsQueryable().Where(x => x.Id == reportQueryDto.MatchId).Select(x => x.MatchName).First() ?? string.Empty,
                TotalStake = totalStake ?? 0,
                TotalPayout = totalPayout ?? 0,
                TotalPaid = totalPaid ?? 0,
                TotalUnPaid = totalUnPaid ?? 0,
                Profit = (totalStake ?? 0) - (totalPayout ?? 0),
                ProfitRate = (totalStake ?? 0) == 0 ? 0 : ((totalStake ?? 0) - (totalPayout ?? 0)) / (totalStake ?? 0)
            };
        }
    }
}
