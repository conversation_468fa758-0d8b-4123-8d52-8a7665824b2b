﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class EncodeUtils
    {
        public static string ToMd5(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            string salt = "|JNTM@2025";

            using var md5 = MD5.Create();
            var bytes = Encoding.UTF8.GetBytes(input + salt);
            var hashBytes = md5.ComputeHash(bytes);
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
        }
    }
}
