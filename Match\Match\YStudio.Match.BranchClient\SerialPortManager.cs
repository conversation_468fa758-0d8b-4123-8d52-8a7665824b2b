﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

namespace YStudio.Match.BranchClient
{
    public class SerialPortManager : IDisposable
    {
        #region 属性和字段
        private SerialPort _serialPort;
        private bool _disposed = false;
        private System.Timers.Timer _connectionMonitorTimer;
        private int _reconnectAttempts = 0;
        private const int MAX_RECONNECT_ATTEMPTS = 5;

        public string PortName { get; private set; }
        public int BaudRate { get; private set; }
        public bool IsConnected => _serialPort?.IsOpen ?? false;
        #endregion

        #region 事件
        public event Action<string> DataReceived;
        public event Action<string> StatusChanged;
        public event Action<bool> ConnectionStateChanged; // 连接状态变化事件
        #endregion

        #region 构造函数
        public SerialPortManager(string portName, int baudRate = 9600)
        {
            PortName = portName;
            BaudRate = baudRate;

            InitializeSerialPort();
            InitializeConnectionMonitor();
        }
        #endregion

        #region 初始化方法
        private void InitializeSerialPort()
        {
            DisposeSerialPort();

            _serialPort = new SerialPort(PortName, BaudRate)
            {
                Parity = Parity.None,
                DataBits = 8,
                StopBits = StopBits.One,
                Handshake = Handshake.None,
                ReadTimeout = 1000,
                WriteTimeout = 1000
            };

            _serialPort.DataReceived += SerialPort_DataReceived;
            _serialPort.ErrorReceived += SerialPort_ErrorReceived;
        }

        private void InitializeConnectionMonitor()
        {
            _connectionMonitorTimer = new System.Timers.Timer(3000); // 每3秒检查一次连接
            _connectionMonitorTimer.Elapsed += CheckConnectionStatus;
            _connectionMonitorTimer.AutoReset = true;
            _connectionMonitorTimer.Start();
        }
        #endregion

        #region 连接管理
        public bool Open()
        {
            try
            {
                if (_serialPort != null && !_serialPort.IsOpen)
                {
                    _serialPort.Open();
                    OnStatusChanged($"串口 {PortName} 打开成功");
                    OnConnectionStateChanged(true);
                    _reconnectAttempts = 0; // 重置重试计数器
                    return true;
                }
                return _serialPort?.IsOpen ?? false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"打开串口失败: {ex.Message}");
                return false;
            }
        }

        public void Close()
        {
            try
            {
                if (_serialPort != null && _serialPort.IsOpen)
                {
                    _serialPort.Close();
                    OnStatusChanged($"串口 {PortName} 已关闭");
                    OnConnectionStateChanged(false);
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"关闭串口失败: {ex.Message}");
            }
        }

        public bool Reconnect()
        {
            Close();
            Thread.Sleep(1000); // 等待资源释放

            InitializeSerialPort(); // 重新初始化串口对象
            bool success = Open();

            if (!success && _reconnectAttempts < MAX_RECONNECT_ATTEMPTS)
            {
                _reconnectAttempts++;
                OnStatusChanged($"尝试重新连接 ({_reconnectAttempts}/{MAX_RECONNECT_ATTEMPTS})");
                Thread.Sleep(2000); // 等待2秒再试
                return Reconnect();
            }

            return success;
        }

        private void CheckConnectionStatus(object sender, ElapsedEventArgs e)
        {
            // 如果串口对象为null或已关闭，尝试重连
            if (_serialPort == null || !_serialPort.IsOpen)
            {
                OnStatusChanged("检测到连接断开，尝试重新连接...");
                Reconnect();
            }
        }
        #endregion

        #region 串口事件处理
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (_serialPort == null || !_serialPort.IsOpen) return;

                string data = _serialPort.ReadLine().Trim();
                DataReceived?.Invoke(data);
            }
            catch (TimeoutException)
            {
                OnStatusChanged("读取数据超时");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"数据接收错误: {ex.Message}");
                Reconnect(); // 自动重连
            }
        }

        private void SerialPort_ErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            OnStatusChanged($"串口错误: {e.EventType}");
            Reconnect(); // 发生错误时自动重连
        }
        #endregion

        #region 资源清理
        private void DisposeSerialPort()
        {
            if (_serialPort != null)
            {
                try
                {
                    _serialPort.DataReceived -= SerialPort_DataReceived;
                    _serialPort.ErrorReceived -= SerialPort_ErrorReceived;

                    if (_serialPort.IsOpen)
                    {
                        _serialPort.Close();
                        OnConnectionStateChanged(false);
                    }

                    _serialPort.Dispose();
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"释放串口资源错误: {ex.Message}");
                }
                finally
                {
                    _serialPort = null;
                }
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _connectionMonitorTimer?.Stop();
                _connectionMonitorTimer?.Dispose();
                DisposeSerialPort();
            }
        }
        #endregion

        #region 事件触发方法
        protected virtual void OnDataReceived(string data)
        {
            DataReceived?.Invoke(data);
        }

        protected virtual void OnStatusChanged(string status)
        {
            StatusChanged?.Invoke(status);
        }

        protected virtual void OnConnectionStateChanged(bool isConnected)
        {
            ConnectionStateChanged?.Invoke(isConnected);
        }
        #endregion

        #region 静态方法
        public static string[] GetAvailablePorts()
        {
            return SerialPort.GetPortNames();
        }
        #endregion
    }

}
