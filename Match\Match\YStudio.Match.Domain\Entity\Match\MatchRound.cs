﻿namespace YStudio.Match.Domain
{

    [SugarTable("t_match_round")]
    public class MatchRound
    {

        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        public int MatchId { get; set; }

        public int Round { get; set; }

        public DateTime StartTime { get; set; }

        public DateTime EndTime { get; set; }

        public int? Interval { get; set; }

        public ERoundStatus Status { get; set; }

        public string? Result { get; set; }

        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 对应的定时任务id
        /// </summary>
        public string? TaskId { get; set; }

    }
}
