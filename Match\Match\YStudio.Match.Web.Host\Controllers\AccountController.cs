﻿using Microsoft.AspNetCore.Mvc;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Domain.Dto.Account;
using YStudio.Match.Domain.Enums;
using YStudio.Match.Repository;

namespace YStudio.Match.Web.Host.Controllers
{
    public class AccountController : ApiControllerBase
    {

        [Autowired]
        private AccountService accountService;

        [Autowired]
        private ICurrentUserService currentUserService;


        [Autowired]
        protected readonly DbContext dbContext;

        [HttpPost]
        public async Task<PaginatedList<Account>> Fetch([FromBody] AccountQueryDto dto)
        {
            if (currentUserService.AccountType == (int)EAccountType.Agent)
            {
                dto.BranchIds = dbContext.Branchs.AsQueryable()
                    .Where(b => b.AccountId == currentUserService.UserId)
                    .Select(b => b.Id)
                    .ToList();
            }

            return await accountService.GetPaginatedListAsync(dto);
        }

        [HttpPost]
        public async Task<ResultBean<Account>> Update([FromBody] Account account)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");

            var res = await dbContext.Accounts.AsUpdateable(account)
                .IgnoreColumns(a => new { a.LoginAccount, a.LoginPassword })
                .ExecuteCommandAsync();
            return ResultBean<Account>.Send(account);
        }

        [HttpPost]
        public async Task<ResultBean<Account>> Create([FromBody] Account account)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");

            var newAccount = await accountService.AddAccount(account);
            return ResultBean<Account>.Send(newAccount);
        }

        [HttpPost]
        public async Task<ResultBean<Account>> ResetPassword([FromBody] Account dto)
        {

            Account account = await dbContext.Accounts.AsQueryable()
                .Where(x => x.Id == dto.Id)
                .FirstAsync();

            Throws.IfNullOrEmpty(account, "账户不存在");

            account.LoginPassword = EncodeUtils.ToMd5(dto.LoginPassword);
            var res = await dbContext.Accounts.AsUpdateable(account)
                .UpdateColumns(a => new { a.LoginPassword })
                .ExecuteCommandAsync();

            return ResultBean<Account>.Success(account);
        }
    }
}
