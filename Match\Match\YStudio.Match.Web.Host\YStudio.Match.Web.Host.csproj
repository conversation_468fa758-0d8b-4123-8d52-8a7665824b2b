﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ProduceReferenceAssembly>True</ProduceReferenceAssembly>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IGeekFan.AspNetCore.Knife4jUI" Version="0.0.16" />
    <PackageReference Include="NAutowired" Version="2.3.1" />
    <PackageReference Include="NetCoreServer" Version="6.7.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\YStudio.Infrastructure\YStudio.Infrastructure.csproj" />
    <ProjectReference Include="..\YStudio.Match.CoreService\YStudio.Match.CoreService.csproj" />
    <ProjectReference Include="..\YStudio.Match.Domain\YStudio.Match.Domain.csproj" />
  </ItemGroup>

</Project>
