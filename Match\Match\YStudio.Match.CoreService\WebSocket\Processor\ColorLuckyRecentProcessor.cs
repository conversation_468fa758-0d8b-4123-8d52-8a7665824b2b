﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;
using YStudio.Match.Domain.Enums;
using YStudio.Match.SpinWin;
using static FreeSql.Internal.GlobalFilter;

namespace YStudio.Match.CoreService.WebSocket.Processor
{
    [Component]
    public class ColorLuckyRecentProcessor : ICommandProcessor<string>
    {
        [Autowired]
        protected readonly MatchService matchService;

        public async Task ProcessAsync(WebSocketSession session, string data)
        {
            int matchId = EMatch.ColorLucky;
            DateTime now = DateTime.Now;

            //1.获取最新期数数据
            var recents = await matchService.GetRecentRoundsAsync(matchId, 3);
            //2.获取下一期数据
            var currentRound = await matchService.GetCurrentRoundAsync(matchId, now);

            var result = new ColorLuckyResult
            {
                Command = ECommand.ColorLuckyRecentResult,
                MatchId = matchId,
                CurrentRound = currentRound.Round,
                CurrentTime = DateTimeUtils.ToTimestampMilliseconds(now),
                NextRound = currentRound.Round,
                NextTime = DateTimeUtils.ToTimestampMilliseconds(currentRound.EndTime),
                RecentResult = recents.Select(item => new ColorLuckyRoundResult
                {
                    Round = item.Round,
                    RoundTime = DateTimeUtils.ToTimestampMilliseconds(item.EndTime),
                    Result = JsonConvert.DeserializeObject<IList<int>>(item.Result),
                }).ToList()
            };
            // 发送结果给当前连接客户端
            session.SendTextAsync(JsonUtils.SerializeObject(result));
        }
    }
}
