﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Repository;
using YStudio.Match.Web.Host.Controllers;
using YStudio.Match.Web.Host.Services;

namespace YStudio.Match.Web.Host.Controllers
{
    public class ConfigController : ApiControllerBase
    {

        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly ICurrentUserService currentUserService;


        [HttpPost]
        public async Task<ResultBean<Config>> Update([FromBody] Config config)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");
            var res = await dbContext.Configs.UpdateAsync(config);
            return ResultBean<Config>.Send(config);
        }

        [HttpPost]
        public async Task<ResultBean<Config>> Get([FromBody] Config config)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");
            var res = await dbContext.Configs.GetSingleAsync(c => c.ConfigKey == config.ConfigKey);
            return ResultBean<Config>.Send(res);
        }



    }
}
