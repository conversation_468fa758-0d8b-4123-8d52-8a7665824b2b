﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.CarsRacing
{
    public class CarOption
    {
        public int OptionId { get; set; }

        public string? OptionName { get; set; }

        public decimal Odds { get; set; }

        public EOptionType Type { get; set; }

        public IList<int> Numbers { get; set; } = new List<int>();

        public CarOption(int optionId, string? optionName, decimal odds, EOptionType type , IList<int> numbers)
        {
            OptionId = optionId;
            OptionName = optionName;
            Odds = odds;
            Numbers = numbers;
            Type = type;
        }
        public CarOption(int optionId, string? optionName, decimal odds, EOptionType type)
        {
            OptionId = optionId;
            OptionName = optionName;
            Odds = odds;
            Type = type;
        }
    }
}
