﻿using Azure.Core;
using FreeRedis;
using FreeScheduler;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NAutowired.Core.Extensions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.Authorization;
using YStudio.Infrastructure.WebSocket;

namespace YStudio.Infrastructure.Extensions
{
    public static partial class Extensions
    {
        public static void AddFreeScheduler(this IServiceCollection services, RedisClient? redisClient)
        {
            services.AddSingleton<Scheduler>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<Scheduler>>();

                Scheduler scheduler = new FreeSchedulerBuilder()
                    .UseTimeZone(TimeSpan.FromHours(3)) //默认为UTC时间，国内可指定时区+8，任务将按本地时间执行
                    .UseScanInterval(TimeSpan.FromMilliseconds(50.0))
                    .OnExecuting(async task =>
                    {
                        try
                        {
                            logger.LogInformation($"Schedule Task Handler");
                            using (var scope = sp.CreateAsyncScope())
                            {
                                var scheduleTaskHandler = scope.ServiceProvider.GetServiceWithAutowired<IScheduleTaskHandler>();
                                await scheduleTaskHandler.OnExecutingAsync(task);
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"Error processing message");
                        }
                    })
                    .UseStorage(redisClient)
                    .Build();

                return scheduler;
            });
        }
    }
}
