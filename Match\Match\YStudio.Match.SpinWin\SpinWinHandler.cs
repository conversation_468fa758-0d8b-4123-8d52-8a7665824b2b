﻿using Microsoft.VisualBasic.FileIO;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using YStudio.Match.SpinWin;

namespace YStudio.Match.SpinWin
{
    public class SpinWinHandler
    {
        public static IList<SpinNumber> Numbers { get; set; } = new List<SpinNumber>();

        public static IList<SpinOption> Options { get; set; } = new List<SpinOption>();

        private static Random random = new(Guid.NewGuid().GetHashCode());

        public StringBuilder LogMessage = new StringBuilder();

        static SpinWinHandler()
        {
            Numbers.Add(new(0, EColor.Green, null, null, null, null));
            Numbers.Add(new(1, EColor.Red, false, ESize.Small, EDozen.First, ELetterSector.D));
            Numbers.Add(new(2, EColor.Black, true, ESize.Small, EDozen.First, ELetterSector.A));
            Numbers.Add(new(3, EColor.Red, false, ESize.Small, EDozen.First, ELetterSector.F));
            Numbers.Add(new(4, EColor.Black, true, ESize.Small, EDozen.First, ELetterSector.A));
            Numbers.Add(new(5, EColor.Red, false, ESize.Small, EDozen.First, ELetterSector.D));
            Numbers.Add(new(6, EColor.Black, true, ESize.Small, EDozen.First, ELetterSector.B));
            Numbers.Add(new(7, EColor.Red, false, ESize.Small, EDozen.First, ELetterSector.F));
            Numbers.Add(new(8, EColor.Black, true, ESize.Small, EDozen.First, ELetterSector.C));
            Numbers.Add(new(9, EColor.Red, false, ESize.Small, EDozen.First, ELetterSector.E));
            Numbers.Add(new(10, EColor.Black, true, ESize.Small, EDozen.First, ELetterSector.C));
            Numbers.Add(new(11, EColor.Black, false, ESize.Small, EDozen.First, ELetterSector.C));
            Numbers.Add(new(12, EColor.Red, true, ESize.Small, EDozen.First, ELetterSector.F));
            Numbers.Add(new(13, EColor.Black, false, ESize.Small, EDozen.Second, ELetterSector.B));
            Numbers.Add(new(14, EColor.Red, true, ESize.Small, EDozen.Second, ELetterSector.E));
            Numbers.Add(new(15, EColor.Black, false, ESize.Small, EDozen.Second, ELetterSector.A));
            Numbers.Add(new(16, EColor.Red, true, ESize.Small, EDozen.Second, ELetterSector.D));
            Numbers.Add(new(17, EColor.Black, false, ESize.Small, EDozen.Second, ELetterSector.B));
            Numbers.Add(new(18, EColor.Red, true, ESize.Small, EDozen.Second, ELetterSector.E));
            Numbers.Add(new(19, EColor.Red, false, ESize.Big, EDozen.Second, ELetterSector.A));
            Numbers.Add(new(20, EColor.Black, true, ESize.Big, EDozen.Second, ELetterSector.D));
            Numbers.Add(new(21, EColor.Red, false, ESize.Big, EDozen.Second, ELetterSector.A));
            Numbers.Add(new(22, EColor.Black, true, ESize.Big, EDozen.Second, ELetterSector.E));
            Numbers.Add(new(23, EColor.Red, false, ESize.Big, EDozen.Second, ELetterSector.C));
            Numbers.Add(new(24, EColor.Black, true, ESize.Big, EDozen.Second, ELetterSector.D));
            Numbers.Add(new(25, EColor.Red, false, ESize.Big, EDozen.Third, ELetterSector.B));
            Numbers.Add(new(26, EColor.Black, true, ESize.Big, EDozen.Third, ELetterSector.F));
            Numbers.Add(new(27, EColor.Red, false, ESize.Big, EDozen.Third, ELetterSector.B));
            Numbers.Add(new(28, EColor.Black, true, ESize.Big, EDozen.Third, ELetterSector.F));
            Numbers.Add(new(29, EColor.Black, false, ESize.Big, EDozen.Third, ELetterSector.E));
            Numbers.Add(new(30, EColor.Red, true, ESize.Big, EDozen.Third, ELetterSector.C));
            Numbers.Add(new(31, EColor.Black, false, ESize.Big, EDozen.Third, ELetterSector.E));
            Numbers.Add(new(32, EColor.Red, true, ESize.Big, EDozen.Third, ELetterSector.A));
            Numbers.Add(new(33, EColor.Black, false, ESize.Big, EDozen.Third, ELetterSector.D));
            Numbers.Add(new(34, EColor.Red, true, ESize.Big, EDozen.Third, ELetterSector.B));
            Numbers.Add(new(35, EColor.Black, false, ESize.Big, EDozen.Third, ELetterSector.F));
            Numbers.Add(new(36, EColor.Red, true, ESize.Big, EDozen.Third, ELetterSector.C));

            Options.Add(new(1001, "A", 6, Numbers.Where(x => x.Sector == ELetterSector.A).Select(x => x.Number).ToList()));
            Options.Add(new(1002, "B", 6, Numbers.Where(x => x.Sector == ELetterSector.B).Select(x => x.Number).ToList()));
            Options.Add(new(1003, "C", 6, Numbers.Where(x => x.Sector == ELetterSector.C).Select(x => x.Number).ToList()));
            Options.Add(new(1004, "D", 6, Numbers.Where(x => x.Sector == ELetterSector.D).Select(x => x.Number).ToList()));
            Options.Add(new(1005, "E", 6, Numbers.Where(x => x.Sector == ELetterSector.E).Select(x => x.Number).ToList()));
            Options.Add(new(1006, "F", 6, Numbers.Where(x => x.Sector == ELetterSector.F).Select(x => x.Number).ToList()));


            string json = JsonSerializer.Serialize(Numbers, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            //Options.Add(new(1007, "0", 36, new List<int> { 0 }));
            //Options.Add(new(1008, "1", 36, new List<int> { 1 }));
            //Options.Add(new(1009, "2", 36, new List<int> { 2 }));
            //Options.Add(new(1010, "3", 36, new List<int> { 3 }));
            //Options.Add(new(1011, "4", 36, new List<int> { 4 }));
            //Options.Add(new(1012, "5", 36, new List<int> { 5 }));
            //Options.Add(new(1013, "6", 36, new List<int> { 6 }));
            //Options.Add(new(1014, "7", 36, new List<int> { 7 }));
            //Options.Add(new(1015, "8", 36, new List<int> { 8 }));
            //Options.Add(new(1016, "9", 36, new List<int> { 9 }));
            //Options.Add(new(1017, "10", 36, new List<int> { 10 }));
            //Options.Add(new(1018, "11", 36, new List<int> { 11 }));
            //Options.Add(new(1019, "12", 36, new List<int> { 12 }));
            //Options.Add(new(1020, "13", 36, new List<int> { 13 }));
            //Options.Add(new(1021, "14", 36, new List<int> { 14 }));
            //Options.Add(new(1022, "15", 36, new List<int> { 15 }));
            //Options.Add(new(1023, "16", 36, new List<int> { 16 }));
            //Options.Add(new(1024, "17", 36, new List<int> { 17 }));
            //Options.Add(new(1025, "18", 36, new List<int> { 18 }));
            //Options.Add(new(1026, "19", 36, new List<int> { 19 }));
            //Options.Add(new(1027, "20", 36, new List<int> { 20 }));
            //Options.Add(new(1028, "21", 36, new List<int> { 21 }));
            //Options.Add(new(1029, "22", 36, new List<int> { 22 }));
            //Options.Add(new(1030, "23", 36, new List<int> { 23 }));
            //Options.Add(new(1031, "24", 36, new List<int> { 24 }));
            //Options.Add(new(1032, "25", 36, new List<int> { 25 }));
            //Options.Add(new(1033, "26", 36, new List<int> { 26 }));
            //Options.Add(new(1034, "27", 36, new List<int> { 27 }));
            //Options.Add(new(1035, "28", 36, new List<int> { 28 }));
            //Options.Add(new(1036, "29", 36, new List<int> { 29 }));
            //Options.Add(new(1037, "30", 36, new List<int> { 30 }));
            //Options.Add(new(1038, "31", 36, new List<int> { 31 }));
            //Options.Add(new(1039, "32", 36, new List<int> { 32 }));
            //Options.Add(new(1040, "33", 36, new List<int> { 33 }));
            //Options.Add(new(1041, "34", 36, new List<int> { 34 }));
            //Options.Add(new(1042, "35", 36, new List<int> { 35 }));
            //Options.Add(new(1043, "36", 36, new List<int> { 36 }));

            for (int i = 0; i <= 36; i++)
            {
                Options.Add(new(1007 + i, $"{i}", 36, new List<int> { i }));
            }

            Options.Add(new(1044, "1~12", 3, Numbers.Where(x => x.Dozens == EDozen.First).Select(x => x.Number).ToList()));
            Options.Add(new(1045, "13~24", 3, Numbers.Where(x => x.Dozens == EDozen.Second).Select(x => x.Number).ToList()));
            Options.Add(new(1046, "25~36", 3, Numbers.Where(x => x.Dozens == EDozen.Third).Select(x => x.Number).ToList()));
            Options.Add(new(1047, "Even", 2, Numbers.Where(x => x.Even == true).Select(x => x.Number).ToList()));
            Options.Add(new(1048, "Odd", 2, Numbers.Where(x => x.Even == false).Select(x => x.Number).ToList()));
            Options.Add(new(1049, "Red", 2, Numbers.Where(x => x.Color == EColor.Red).Select(x => x.Number).ToList()));
            Options.Add(new(1050, "Black", 2, Numbers.Where(x => x.Color == EColor.Black).Select(x => x.Number).ToList()));
            Options.Add(new(1051, "1-18", 2, Numbers.Where(x => x.Size == ESize.Small).Select(x => x.Number).ToList()));
            Options.Add(new(1052, "19-36", 2, Numbers.Where(x => x.Size == ESize.Big).Select(x => x.Number).ToList()));
        }


        /// <summary>
        /// 计算本次的数字
        /// </summary>
        /// <param name="stakes">订单金额信息</param>
        /// <param name="reserveRatio">保留订单额比例 5%;</param>
        /// <returns></returns>
        public SpinNumber Handle(Dictionary<string, decimal> stakes, decimal reserveRatio = 0.20m, int minpayoutRate = 5)
        {

            ////每个选项的订单金额
            //stakes = new()
            //{
            //    {"1001_A", 500 },
            //    {"1002_B", 500 },
            //    {"1003_C", 500 },
            //    {"1004_D", 500 },
            //    {"1005_F", 500 },
            //    { "1008_1", 1000}
            //};

            //每个数字的赔付金额
            ConcurrentDictionary<int, decimal> Payouts = new();

            //每个数字的订单金额
            ConcurrentDictionary<int, decimal> NumberStakes = new();

            //累计投注金额
            decimal totalStake = stakes.Values.Sum();
            LogMessage.AppendLine($"累计下单金额: {totalStake}");


            var keys = stakes.Keys.ToList();



            foreach (var key in keys)
            {
                //如果key不符合格式，跳过
                int? optionId = int.Parse(key.Split("_")[0]);
                if (!optionId.HasValue)
                    continue;

                //下单的Option
                var option = Options.FirstOrDefault(x => x.OptionId == optionId.Value);
                if (option == null)
                    continue;

                //该选项中包含每个数字 都需要赔付的金额
                decimal payout = stakes[key] * option.Odds;

                //该选项中包含的每个数字的订单金额


                //统计每个数字的赔付金额
                option.Numbers.ToList().ForEach(x =>
                {
                    //统计每个数字的订单金额
                    NumberStakes.AddOrUpdate(x, stakes[key], (k, v) => v + stakes[key]);
                    //统计每个数字的赔付金额
                    Payouts.AddOrUpdate(x, payout, (k, v) => v + payout);
                });
            }

            //输出每个数字的订单金额 和赔付金额
            foreach (var numberStake in NumberStakes.OrderBy(x => x.Key))
            {
                LogMessage.AppendLine($"数字 {numberStake.Key} 订单金额: {numberStake.Value} 赔付金额: {Payouts[numberStake.Key]}");
            }

            LogMessage.AppendLine($"吃分比例: {reserveRatio * 100} %");
            //预计赔付金额
            var estPayout = totalStake * (1 - reserveRatio);
            LogMessage.AppendLine($"预计赔付金额: {estPayout}");

            //从Payout中找出值小于等于estPayout的第一个数字
            /**
             * 1.优先找小于等于estPayout的数字
             * 2. 没有的话，找大于estPayout的第一个数字
             * 3.还没有的话随机一个
             */

            //随机号码
            int openNumber = random.Next(0, Numbers.Count);

            //在等于赔付金额的数字列表随机取一个
            var equalList = Payouts.Where(x => x.Value == estPayout).ToList();
            if (equalList.Count > 0)
            {
                int index = random.Next(0, equalList.Count);
                openNumber = equalList[index].Key;
                LogMessage.AppendLine($"符合吃分规则1,在等于吃分赔付金额规则的{equalList.Count}个数字中选择,结算数字为 {openNumber}");
                return Numbers[openNumber];
            }

            //在小于赔付金额的数字列表取前3个中随机一个
            var lessList = Payouts.Where(x => x.Value < estPayout).Where(x => x.Value > 0).OrderBy(x => x.Value).Take(10).ToList();
            if (lessList.Count > 0)
            {
                int index = random.Next(0, lessList.Count);
                openNumber = lessList[index].Key;
                LogMessage.AppendLine($"符合吃分规则2,在小于吃分赔付金额规则的{lessList.Count}个数字中选择,结算数字为 {openNumber}");
                return Numbers[openNumber];
            }



            int randomRate = random.Next(1, 101);
            LogMessage.AppendLine($"通吃比例配置为 {minpayoutRate} % ,本次随机概率为 {randomRate} %");

            //在通吃概率范围内
            if (randomRate <= minpayoutRate)
            {
                //1.优先开不用赔付的数字
                //2.没有这个数字的话，开赔付最少的数字
                var zeroPayoutList = Numbers.Where(x => !Payouts.ContainsKey(x.Number)).ToList();
                if (zeroPayoutList.Count > 0)
                {
                    int index = random.Next(0, zeroPayoutList.Count);
                    openNumber = zeroPayoutList[index].Number;
                    LogMessage.AppendLine($"符合通吃规则,0赔付,结算数字为 {openNumber}");
                    return Numbers[openNumber];
                }
                else
                {
                    
                    var minPayout = Payouts.OrderBy(x => x.Value).First();
                    //最小赔付金额可能有多个 需要随机取一个
                    if(Payouts.Values.Count(x => x == minPayout.Value) > 1)
                    {
                        var minPayoutList = Payouts.Where(x => x.Value == minPayout.Value).ToList();
                        int index = random.Next(0, minPayoutList.Count);
                        minPayout = minPayoutList[index];
                    }

                    openNumber = minPayout.Key;
                    LogMessage.AppendLine($"符合最低赔付规则,赔付 {minPayout} ,结算数字为 {openNumber}");
                    return Numbers[openNumber];
                }
            }

            //在大于赔付金额的数字列表取第一个
            //var greaterList = Payouts.Where(x => x.Value > estPayout).OrderBy(x => x.Value).ToList();
            //if (greaterList.Count > 0)
            //{
            //    openNumber = greaterList[0].Key;
            //    return Numbers[openNumber];
            //}
            LogMessage.AppendLine($"不符合吃分规则,未进入通吃规则,随机结算数字为 {openNumber}");
            return Numbers[openNumber];
        }


        /// <summary>
        /// 查询数字对应的选项Id
        /// </summary>
        /// <param name="spinNumber"></param>
        /// <returns></returns>
        public List<int> GetOptionsByNumber(SpinNumber spinNumber)
        {
            return Options.Where(x => x.Numbers.Contains(spinNumber.Number)).Select(x => x.OptionId).ToList();
        }
    };

}
