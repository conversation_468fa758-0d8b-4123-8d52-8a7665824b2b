﻿using System.Security.Claims;
using YStudio.Infrastructure;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.Web.Host.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        [Autowired]
        private readonly IHttpContextAccessor httpContextAccessor;

        public int UserId
        {
            get
            {
                return int.Parse(httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == ClaimTypes.NameIdentifier).First().Value);
            }
        }

        public string RemoteIP
        {
            get
            {
                var remoteIp = httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress;
                if (remoteIp == null) return string.Empty;

                if (remoteIp.IsIPv4MappedToIPv6)
                    remoteIp = remoteIp.MapToIPv4();
                return remoteIp.ToString();
            }
        }

        public int BranchId
        {
            get
            {
                return int.Parse(httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == "BranchId").First().Value);
            }
        }

        public string BranchName
        {
            get
            {
                return httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == "BranchName").First().Value;
            }
        }

        public string NickName
        {
            get
            {
                return httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == ClaimTypes.Name).First().Value;
            }
        }

        public int AccountType
        {
            get { return (int)Enum.Parse<EAccountType>(httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == ClaimTypes.Role).First().Value); }
        }

    }
}
