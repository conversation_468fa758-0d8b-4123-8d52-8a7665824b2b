﻿using Microsoft.Web.WebView2.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace YStudio.Match.BranchClient
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            await InitializeWebView();
            //InitializeSerialManager();

            webView.CoreWebView2.Navigate("http://magic.yunfanke.cn/branch");
            webView.CoreWebView2.AddHostObjectToScript("jsBridge", new JsBridge());
        }

        private SerialPortManager _serialManager;

        private void InitializeSerialManager()
        {
            // 获取可用串口列表
            var availablePorts = SerialPortManager.GetAvailablePorts();
            if (availablePorts.Length == 0)
            {
                MessageBox.Show("未检测到可用串口");
                return;
            }
            

            // 假设使用第一个检测到的串口
            string portName = availablePorts[0];

            _serialManager = new SerialPortManager(portName, 9600);
            _serialManager.DataReceived += SerialManager_DataReceived;
            _serialManager.StatusChanged += SerialManager_StatusChanged;
            _serialManager.ConnectionStateChanged += SerialManager_ConnectionStateChanged;

            if (!_serialManager.Open())
            {
                // 初始连接失败会自动尝试重连
            }
        }

        private void SerialManager_DataReceived(string data)
        {
            this.Invoke(new Action(() =>
            {
                MessageBox.Show($"收到数据: {data}");
            }));
        }

        private void SerialManager_StatusChanged(string status)
        {
            this.Invoke(new Action(() =>
            {
                MessageBox.Show($"状态切换: {status}");
            }));
        }

        private void SerialManager_ConnectionStateChanged(bool isConnected)
        {
            this.Invoke(new Action(() =>
            {
                MessageBox.Show($"ConnectionStateChanged: {isConnected}");
            }));
        }

        private async Task InitializeWebView()
        {
            string userDataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "UserData");
            Directory.CreateDirectory(userDataFolder);

            var options = new CoreWebView2EnvironmentOptions(language: "zh");
            #if DEBUG
            options.AdditionalBrowserArguments = "--remote-debugging-port=9222";//开启调试
            #endif
            var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder, options);
            await webView.EnsureCoreWebView2Async(environment);
        }
    }
}
