﻿namespace YStudio.Match.Domain
{

    [SugarTable("t_match_round_log")]
    public class MatchRoundLog : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int Id { get; set; }
        public int Round { get; set; }
        public Guid RoundId { get; set; }
        public int MatchId { get; set; }
        public string MatchName { get; set; } = string.Empty;
        public DateTime EndTime { get; set; }
        public string LogMessage { get; set; } = string.Empty;
    }
}
