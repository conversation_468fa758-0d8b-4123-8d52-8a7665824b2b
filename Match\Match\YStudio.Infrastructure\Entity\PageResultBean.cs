﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class PageResultBean<T> : ResultBean<T>
    {
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 15;
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }

        public PageResultBean(T obj, int pageIndex, int pageSize, int totalaCount, int totalPages, string error = "error") : base(obj, "success", error)
        {
            Result = obj;
            TotalCount = totalaCount;
            TotalPages = totalPages;
            PageIndex = pageIndex;
            PageSize = pageSize;

            Code = (obj == null) ? 500 : 200;
            Message = (obj == null) ? error : "success";
        }
        public PageResultBean(PaginatedRequest request, int totalCount, T obj, string error = "error") : base(obj, "success", error)
        {
            Result = obj;
            TotalCount = totalCount;
            TotalPages = (totalCount + request.PageSize - 1) / request.PageSize;
            PageIndex = request.PageIndex;
            PageSize = request.PageSize;

            Code = (obj == null) ? 500 : 200;
            Message = (obj == null) ? error : "success";
        }

        public static PageResultBean<T> Send(T obj, PaginatedRequest request, int totalCount, string error = "error")
        {
            return new PageResultBean<T>(request, totalCount, obj, error);
        }

    }
}
