﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    [SugarTable("t_config")]
    public class Config : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public string ConfigKey { get; set; }

        public string ConfigValue { get; set; }
    }
}
