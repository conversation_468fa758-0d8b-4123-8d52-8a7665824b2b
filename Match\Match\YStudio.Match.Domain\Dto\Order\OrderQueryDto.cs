﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    public class OrderQueryDto : PageableRequest
    {

        public string? Cashier { get; set; }

        public string OrderNo { get; set; } = string.Empty;

        public long? Round { get; set; }

        public int? MatchId { get; set; }

        public int? BranchId { get; set; }
        public int? CashierId { get; set; }
        public int? AgentId { get; set; }
        public List<int>? BranchIds { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }
}
