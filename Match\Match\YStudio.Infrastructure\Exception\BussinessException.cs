﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class BussinessException : BaseException
    {
        public BussinessException(string message) : base(message)
        {
            Code = 500;
        }

        public BussinessException(int code, string message) : base(message)
        {
            Code = code;
        }
    }
}
