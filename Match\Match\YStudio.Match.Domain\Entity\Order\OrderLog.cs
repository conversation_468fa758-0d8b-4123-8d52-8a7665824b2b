﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{

    [SugarTable("t_order_log")]
    public class OrderLog : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public int Type { get; set; }

        public int OperationUser { get; set; }

        public string? Content { get; set; }
    }
}
