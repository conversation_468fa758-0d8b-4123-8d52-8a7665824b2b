﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{

    [SugarTable("t_match_option")]
    public class MatchOption : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        public int MatchId { get; set; }

        public decimal Odds { get; set; }

        public string? OptionName { get; set; }

        public string? Type { get; set; }

        public string? Style { get; set; }
    }
}
