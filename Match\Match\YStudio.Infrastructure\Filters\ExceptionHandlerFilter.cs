﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using NAutowired.Core.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure;

namespace YStudio.Infrastructure
{
    public class ExceptionHandlerFilter : IAsyncExceptionFilter
    {
        private readonly ILogger<ExceptionHandlerFilter> logger;

        public ExceptionHandlerFilter(ILogger<ExceptionHandlerFilter> logger)
        {
            this.logger = logger;
        }

        public Task OnExceptionAsync(ExceptionContext context)
        {
            if (!context.ExceptionHandled)
            {
                if (context.Exception is BussinessException ||
                    context.Exception is AuthorizationException ||
                    context.Exception is ValidateException ||
                    context.Exception is NoPermissionException)
                {
                    BaseException ex = context.Exception as BaseException;
                    logger.LogError(context.Exception, "ERROR111");
                    context.Result = new JsonResult(new { ex.Code, ex.Message });
                }
                else
                {
                    logger.LogError(context.Exception, "ERROR222");
                    //TODO 日志记录
                    context.Result = new JsonResult(new { Code = -1, Message = context.Exception.Message ?? "服务异常,请稍后重试!", Exception = context.Exception });
                }
            }
            context.ExceptionHandled = true;
            return Task.CompletedTask;
        }

    }
}
