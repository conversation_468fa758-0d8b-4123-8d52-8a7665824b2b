﻿using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using YStudio.Infrastructure;
using YStudio.Infrastructure.Authorization;
using YStudio.Match.Branch.Host.Dto;
using YStudio.Match.CoreService;
using YStudio.Match.Domain;

namespace YStudio.Match.Branch.Host.Controllers
{
    [Route("branch/[controller]/[action]")]
    [ApiController]
    public class PassportController : ControllerBase
    {
        [Autowired]
        private readonly BranchService branchService;

        /// <summary>
        /// 账号登录
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ResultBean<AccountDto>> Login([FromBody] LoginDto dto)
        {
            Account account = await branchService.LoginAsync(dto.LoginAccount, dto.LoginPassword);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, account.Id.ToString()),
                new Claim(ClaimTypes.Name, account.NickName),
                new Claim("BranchId", account.BranchId.ToString()),
                new Claim("BranchName", account.Branch.BranchName),
            };

            var settings = InternalApp.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
            DateTime expireAt = DateTime.Now.AddSeconds(settings.ExpireSeconds);

            var token = JwtHelper.buildToken(claims, expireAt);

            var accountDto = account.Adapt<AccountDto>();
            accountDto.BranchName = account.Branch.BranchName;
            accountDto.Token = token;
            accountDto.ExpireAt = expireAt;

            return ResultBean<AccountDto>.Success(accountDto);
        }
    }
}
