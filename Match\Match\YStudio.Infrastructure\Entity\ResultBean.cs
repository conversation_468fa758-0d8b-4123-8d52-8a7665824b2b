﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class ResultBean<T>
    {
        public int Code { get; set; } = 500;
        public string Message { get; set; } = string.Empty;
        public T Result { get; set; }
        public ResultBean(T obj, string success = "success", string error = "Server Busy, Please try again later!")
        {
            Result = obj;
            Code = (obj == null) ? 500 : 200;
            Message = (obj == null) ? error : success;
        }

        public static ResultBean<T> Send(T obj)
        {
            return new ResultBean<T>(obj);
        }

        public static ResultBean<T> Success(T obj)
        {
            return new ResultBean<T>(obj);
        }
    }
}
