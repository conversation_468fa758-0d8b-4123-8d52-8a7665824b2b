﻿using ESCPOS;
using ESCPOS.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using YStudio.Match.PrinterService.Domain;

namespace YStudio.Match.PrinterService
{
    public class OrderPrinterHelper : Printer
    {

        public OrderPrinterHelper()
        {
            Encoding = Encoding.UTF8;
            Columns = 48;
        }
        public void AddTitle(string title)
        {
            AddToCache(
                 Commands.UnderlineModeOn,
                 Commands.CharSizeDoubleHeightAndWidth,
                 Commands.AlignToCenter,
                 title,
                 Commands.CharSizeReset,
                 Commands.UnderlineModeOff,
                 Commands.LF,
                 Commands.LF
             );
        }

        public byte[] GetBytes()
        {
            if (CommandCache == null || CommandCache.Length == 0)
                return Array.Empty<byte>();
            var bytes = CommandCache;
            ClearCache();
            return bytes;
        }

        public void AddSubTitle(string title, string value)
        {
            AddToCache(
                Commands.AlignToLeft,
                Commands.DoubleStrikeOn,
                title,
                Commands.DoubleStrikeOff,
                value,
                Commands.LF
          );
        }

        public void AddOrder(OrderModel order)
        {
            AddTitle(order.Title);
            AddSubTitle("Branch: ", order.BranchName);
            AddSubTitle("Cashier: ", order.Cashier);
            AddSubTitle("Order No: ", order.OrderNo);
            AddSubTitle("Date: ", order.CreateTime.ToString("dd-MM-yyyy HH:mm"));
            AddToCache(HorizontalDoubleLine);
            AddOrderItems(order);
            AddFooter(order);
        }

        public void AddOrderItems(OrderModel order)
        {
            order.Items.ForEach(item =>
            {
                AddToCache(
                    Commands.AlignToLeft,
                    SameLineLeftAndRightAlignedText(order.MatchName, $"SN: {order.Round}"),
                    Commands.LF,
                    SameLineLeftAndRightAlignedText($"{item.OptionName}({item.OptionType})", $"Odds: {item.Odds.ToString("#,0.##")}"),
                    Commands.LF,
                    SameLineLeftAndRightAlignedText($"Stake: {item.Stake.ToString("N2")}", $"Max Payout: { item.MaxPayout.ToString("N2")}"),
                    Commands.LF,
                    Commands.AlignToLeft,
                    HorizontalLine,
                    Commands.LF
                );
            });
        }


        public void AddFooter(OrderModel order)
        {
            AddToCache(
                Commands.DoubleStrikeOn,
                SameLineLeftAndRightAlignedText("Stake", $"TZS {order.Stake?.ToString("N2")}"),
                Commands.LF,
                SameLineLeftAndRightAlignedText("Max Payout", $"TZS {order.MaxPayout?.ToString("N2")}"),
                Commands.LF,
                Commands.DoubleStrikeOff,
                HorizontalDoubleLine,
                Commands.LF
            );

            AddToCache(
                Commands.AlignToCenter,
                Commands.Barcode(BarCodeType.CODE128, order.OrderNo, 80, BarcodeWidth.Thin),
                order.OrderNo,
                Commands.LF,
                Commands.AlignToLeft,
                "Bet responsibly",
                Commands.LF,
                Commands.LF,
                Commands.LF,
                Commands.LF,
                Commands.LF,
                Commands.LF
            );

            AddToCache(Commands.PaperCut);
        }

        public int Print(IntPtr printer)
        {
            if (printer == IntPtr.Zero)
                throw new InvalidOperationException("Printer is invalid.");
            var bytes = GetBytes();
            if (bytes.Length > 0)
            {
                return XPrinter.WriteData(printer, bytes, bytes.Length);
            }
            else
            {
                throw new InvalidOperationException("No content to print.");
            }
        }
    }
}
