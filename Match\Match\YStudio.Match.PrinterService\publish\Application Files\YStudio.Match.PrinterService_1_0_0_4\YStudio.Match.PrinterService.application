﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="YStudio.Match.PrinterService.application" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="Microsoft" asmv2:product="YStudio.Match.PrinterService" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="true" mapFileExtensions="true" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.7.2" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="Application Files\YStudio.Match.PrinterService_1_0_0_4\YStudio.Match.PrinterService.exe.manifest" size="9469">
      <assemblyIdentity name="YStudio.Match.PrinterService.exe" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>Bv22TRtqsmRIIhWSkOURcgysAa9r4fZo+jAJLxQ2q/8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=PC-20240813PQMQ\Administrator" issuerKeyHash="abcf9891428564f9343eeed7087a36a7a3850dc1" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>x+b7Eh+NVxhqNATimTN4ian08fljBio9rWJOHPOZLfk=</DigestValue></Reference></SignedInfo><SignatureValue>PjTi5syPy68fA/1kxbm7K8/nJKWOlm6MZce5IzeIQy+clNF6u7Qp/9iUZN/bRqKAIlP2qNIULB/YhRlwv66moQ1h0jwviAkFoxe/qK2yX3p65d6+qgRxYbKTSkaAU0HNO5EUDEq2a4LHaSZKhHKHRg0FoqTMgJn+qkgqKZumDGk=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>zj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="f92d99f31c4e62ad3d2a0663f9f1f4a989783399e204346a18578d1f12fbe6c7" Description="" Url=""><as:assemblyIdentity name="YStudio.Match.PrinterService.application" version="*******" publicKeyToken="0c3e386fdb5a2d4f" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=PC-20240813PQMQ\Administrator</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>wYb3YjgsoLbeR66T6UdsgqJp/rBIoxPMgFF/BmQ5OCQ=</DigestValue></Reference></SignedInfo><SignatureValue>W078Fj6cffkQBzZH6euZ16aIRtJF4Xd7Dv9UY+Hwc2cZHNM8M4yP4k2olNPiXy1U2p8fCjv1dxhUgQ+kEd1HF2/rHeTzeAw7ewS3HG2rwsDe8jK6QYDXU0l+JMtrvVZOOBaGcR/FDg3/9+7+IavW13WYtBNKHGFLGmKJurpu6+E=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>zj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIICDTCCAXagAwIBAgIQJjxe8bFsYKlJ6zcK0xsdcTANBgkqhkiG9w0BAQsFADBFMUMwQQYDVQQDHjoAUABDAC0AMgAwADIANAAwADgAMQAzAFAAUQBNAFEAXABBAGQAbQBpAG4AaQBzAHQAcgBhAHQAbwByMB4XDTI1MDcwMzA2NTMxMVoXDTI2MDcwMzEyNTMxMVowRTFDMEEGA1UEAx46AFAAQwAtADIAMAAyADQAMAA4ADEAMwBQAFEATQBRAFwAQQBkAG0AaQBuAGkAcwB0AHIAYQB0AG8AcjCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAzj2Xxpmqm1h7V4IBaE4h7BZNCAP0hZORTFW6zyI8tv8vdOmUifXlajKkZK5o6F3zb31HlFutfp3BpHm1jYDrb8BvVCxk/KGl/teT7dW4mpp8OAUuuIVXvK6YsOziDcOWa6r3GkNPlEfFI2QzzETTayJiYlKLL4eJgIWMhMhQZJECAwEAATANBgkqhkiG9w0BAQsFAAOBgQAyhiRewe3nU9OPAoG5GaRonEi5MEMDXHDAhzgGKzsEnD4xi4oL9a0TXXBYwdf6kKyOCT3kq/aICaj0SC7FVrnbMDai/X4ulP5+R87+4CK7knlS2FWTlUTQ/yZy8SRH+A8bextKs7ks3Fa3WSRogHrJDEkqGpmzfbVpyA0OMnKxKg==</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>