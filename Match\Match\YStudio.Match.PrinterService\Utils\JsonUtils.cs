﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;

namespace YStudio.Match.PrinterService.Utils
{
    public class JsonUtils
    {
        /// <summary>
        /// 序列化对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static string SerializeObject<T>(T obj)
        {
            var options = new JsonSerializerSettings()
            {
                Formatting = Formatting.None,
                //全局处理 返回时间格式
                //DateFormatString = "yyyy/MM/dd HH:mm:ss",
                //全局处理 接收时间并做本地化处理
                DateTimeZoneHandling = DateTimeZoneHandling.Local,
                //首字母小写驼峰式命名
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                //忽略循环引用
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                // 空值处理
                //options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                MissingMemberHandling = MissingMemberHandling.Ignore
            };

            return JsonConvert.SerializeObject(obj, options);
        }

        /// <summary>
        /// Json反序列化
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="json"></param>
        /// <returns></returns>
        public static T Deserialize<T>(string json)
        {
            return JsonConvert.DeserializeObject<T>(json);
        }

        /// <summary>
        /// Json反序列化 失败返回默认值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="json"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static T Deserialize<T>(string json, T defaultValue)
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception ex)
            {
                return defaultValue;
            }
        }
    }
}
