﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.Domain.Dto.Account
{
    public class AccountQueryDto : PageableRequest
    {

        public int? BranchId { get; set; }

        public List<int>? BranchIds { get; set; }

        public string? NickName { get; set; }
        public string? BranchName { get; set; }

        public string? LoginAccount { get; set; }

        public string? LoginPassword { get; set; }

        public bool IsAdmin { get; set; }

        public DateTime? LoginAt { get; set; }

        public string? LastLoginIp { get; set; }

        public EAccountType? AccountType { get; set; }


        public int? Status { get; set; }

        public DateTime? EndTime { get; set; }

        public string? FullName { get; set; }
        public string? Mobile { get; set; }

        public string? Email { get; set; }
        public string? AgentName { get; set; }

        
        public int Gender { get; set; }
    }
}
