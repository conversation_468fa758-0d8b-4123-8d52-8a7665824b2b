﻿using SqlSugar;
using System.Configuration;
using System.Xml.Linq;

namespace YStudio.Match.Branch.Host.Services
{
    public static partial class Extensions
    {


        public static void AddSqlSugar(this IServiceCollection services, string name = "DefaultConnection")
        {

            var connectionString = services.BuildServiceProvider()
                .GetRequiredService<IConfiguration>()
                .GetConnectionString(name);


            SqlSugarScope sqlSugar = new SqlSugarScope(new ConnectionConfig()
            {
                DbType = SqlSugar.DbType.MySql,
                ConnectionString = connectionString,
                IsAutoCloseConnection = true,
            }, db =>
            {
                db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    // Console.WriteLine(sql);//输出sql,查看执行sql
                    //******* 获取无参数化 SQL 
                    Console.WriteLine(UtilMethods.GetSqlString(DbType.SqlServer,sql,pars));
                    //File.AppendAllTextAsync("./sql.txt",UtilMethods.GetSqlString(DbType.MySql, sql, pars) + "\r\n");
                };

                db.Aop.DataExecuting = (oldValue, entityInfo) =>
                {
                    if (entityInfo.PropertyName == "CreateBy" && entityInfo.OperationType == DataFilterType.InsertByObject)
                    {
                        entityInfo.SetValue("WMS_AUTO");//修改CreateBy字段

                        /*entityInfo有字段所有参数*/

                        /*oldValue表示当前字段值 等同于下面写法*/
                        //var value=entityInfo.EntityColumnInfo.PropertyInfo.GetValue(entityInfo.EntityValue);

                        /*获取当前列特性*/
                        //5.1.3.23 +
                        //entityInfo.IsAnyAttribute<特性>()
                        //entityInfo.GetAttribute<特性>()
                    }
                };
            });

            services.AddSingleton<ISqlSugarClient>(sqlSugar);

            //ISugarUnitOfWork<DbContext> context = new SugarUnitOfWork<DbContext>(sqlSugar);
            //services.AddSingleton<ISugarUnitOfWork<DbContext>>(context);

            //services.AddSingleton<ISqlSugarClient>(services =>
            //{
            //    var sqlSugar = new SqlSugarScope(new ConnectionConfig()
            //    {
            //        ConnectionString = connectionString,
            //        DbType = DbType.MySql,
            //        IsAutoCloseConnection = true
            //    }, db =>
            //    {
            //        db.Aop.OnLogExecuting = (sql, pars) =>
            //        {
            //            // Console.WriteLine(sql);//输出sql,查看执行sql
            //            //******* 获取无参数化 SQL 
            //            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)

            //            Console.WriteLine(UtilMethods.GetSqlString(DbType.MySql, sql, pars));
            //        };

            //        db.Aop.DataExecuting = (oldValue, entityInfo) =>
            //        {

            //            if (entityInfo.PropertyName == "CreateBy" && entityInfo.OperationType == DataFilterType.InsertByObject)
            //            {
            //                entityInfo.SetValue("WMS_AUTO");//修改CreateBy字段

            //                /*entityInfo有字段所有参数*/

            //                /*oldValue表示当前字段值 等同于下面写法*/
            //                //var value=entityInfo.EntityColumnInfo.PropertyInfo.GetValue(entityInfo.EntityValue);

            //                /*获取当前列特性*/
            //                //5.1.3.23 +
            //                //entityInfo.IsAnyAttribute<特性>()
            //                //entityInfo.GetAttribute<特性>()
            //            }

            //        };

            //    });
            //    return sqlSugar;

            //});


        }
    }
}