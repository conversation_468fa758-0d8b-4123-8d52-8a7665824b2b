﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{

    [SugarTable("t_branch")]
    public class Branch : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public string? BranchName { get; set; }

        public string? Address { get; set; }

        public string? ContactName { get; set; }
        public int? AccountId { get; set; }

        [Navigate(NavigateType.OneToOne, nameof(Branch.AccountId), nameof(Id))]
        public Account? Account { get; set; }

        public ENormalStatus? Status { get; set; }
        public DateTime? EndTime { get; set; }

    }
}
