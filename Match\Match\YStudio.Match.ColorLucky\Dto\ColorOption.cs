﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.ColorLucky
{
    public class ColorOption
    {
        /// <summary>
        /// 订单的数字个数
        /// </summary>
        public int NumberCount { get; set; } = 3;

        /// <summary>
        /// 压中的数字个数
        /// </summary>
        public int WinCount { get; set; } = 0;

        /// <summary>
        /// 赔率
        /// </summary>
        public decimal Odds { get; set; } = 0;

        public ColorOption(int numberCount, int winCount, decimal odds)
        {
            NumberCount = numberCount;
            WinCount = winCount;
            Odds = odds;
        }

        public ColorOption()
        {
        }
    }
}
