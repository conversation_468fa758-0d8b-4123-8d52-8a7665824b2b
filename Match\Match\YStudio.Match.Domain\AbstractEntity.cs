﻿
namespace YStudio.Match.Domain
{
    public class AbstractEntity
    {

        /// <summary>
        /// Desc:记录状态 0正常 -1 删除
        /// Default: 0
        /// Nullable:False
        /// </summary>
        [JsonIgnore] 
        public int? RecordState { get; set; } = 0;

        /// <summary>
        /// Desc:删除时间
        /// Default:
        /// Nullable:True
        /// </summary>    
        [JsonIgnore]
        public DateTime? DeleteTime { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>
        [JsonIgnore]
        public string? CreateBy { get; set; }


        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>     
        [SugarColumn(InsertServerTime = true)]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>     
        [JsonIgnore]
        [SugarColumn(UpdateServerTime = true)]
        public DateTime? UpdateTime { get; set; }
    }
}
