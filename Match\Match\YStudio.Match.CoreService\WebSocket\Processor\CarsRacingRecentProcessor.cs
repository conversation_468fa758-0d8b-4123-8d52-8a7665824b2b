﻿using Microsoft.AspNetCore.Routing;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;
using YStudio.Match.Domain.Enums;
using YStudio.Match.SpinWin;

namespace YStudio.Match.CoreService.WebSocket.Processor
{
    [Component]
    public class CarsRacingRecentProcessor : ICommandProcessor<string>
    {

        [Autowired]
        protected readonly MatchService matchService;

        public async Task ProcessAsync(WebSocketSession session, string data)
        {
            int matchId = EMatch.CarsRacing;
            DateTime now = DateTime.Now;

            //1.获取最新期数数据
            var recents = await matchService.GetRecentRoundsAsync(matchId, 20);
            //2.获取下一期数据
            var currentRound = await matchService.GetCurrentRoundAsync(matchId, now);

            var result = new CarRacingResult
            {
                Command = ECommand.CarRecentResult,
                MatchId = matchId,
                CurrentRound = currentRound.Round,
                CurrentTime = DateTimeUtils.ToTimestampMilliseconds(now),
                NextRound = currentRound.Round,
                NextTime = DateTimeUtils.ToTimestampMilliseconds(currentRound.EndTime),
                RecentResult = recents.Select(item => new CarRacingRoundResult
                {
                    Round = item.Round,
                    RoundTime = DateTimeUtils.ToTimestampMilliseconds(item.EndTime),
                    Result = JsonConvert.DeserializeObject<IList<int>>(item.Result),
                }).ToList(),
                Jackpot = new List<JackpotItem>()
            };
            // 发送结果给当前连接客户端
            session.SendTextAsync(JsonUtils.SerializeObject(result));
        }
    }
}
