﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Match.Domain;

namespace YStudio.Match.CoreService
{
    [Service]
    public class BranchService
    {
        [Autowired]
        protected readonly DbContext dbContext;
        [Autowired]
        protected readonly ICurrentUserService currentUserService;


        public async Task<Account> LoginAsync(string loginAccount, string loginPassword)
        {
            var account = await dbContext.Accounts.AsQueryable()
                .Where(x => x.LoginAccount == loginAccount)
                .Where(x => x.LoginPassword == EncodeUtils.ToMd5(loginPassword))
                .Where(x => x.AccountType == Domain.Enums.EAccountType.Cashier)
                .Includes(x => x.Branch)
                .FirstAsync();

            Throws.IfNullOrEmpty(account, "Account does not exist or password error");
            Throws.If(account.Status != ENormalStatus.Normal, "Account is disabled");

            await dbContext.Accounts.UpdateAsync(a => new Account
            {
                LastLoginIp = currentUserService.RemoteIP,
                LastLoginTime = DateTime.Now
            }, a => a.Id == account.Id);

            return account;
        }

        public async Task<Account> GetProfile(int accountId)
        {
            var account = await dbContext.Accounts.AsQueryable()
                  .Includes(x => x.Branch)
                  .Where(x => x.Id == accountId)
                  .IgnoreColumns(x => new { x.LoginPassword })
                  .SingleAsync();

            Throws.IfNullOrEmpty(account, "Account does not exist");

            return account;
        }

        public async Task<PaginatedList<Branch>> GetList()
        {
            return await dbContext.Branchs.AsQueryable().PaginatedListAsync(1, 50);
        }
    }
}
