﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    [SugarTable("t_order")]
    public class Order : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        public int BranchId { get; set; }

        [Navigate(NavigateType.OneToOne, nameof(BranchId), nameof(Domain.Branch.Id))]
        public Branch? Branch { get; set; }

        public string? BranchName { get; set; }

        public string? Cashier { get; set; }

        public int? CashierId { get; set; }

        public string OrderNo { get; set; } = string.Empty;

        public long? Round { get; set; }

        public Guid? RoundId { get; set; }

        public int MatchId { get; set; }

        public string? MatchName { get; set; }

        public DateTime? ExpireTime { get; set; }

        public DateTime? PrintTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DateTime? CancelTime { get; set; }

        public EOrderState Status { get; set; } = 0;

        public DateTime? PayTime { get; set; }

        public decimal? MaxPayout { get; set; } = 0;

        public decimal? ActualPayout { get; set; } = 0;

        public decimal? Stake { get; set; } = 0;

        public int SettledId { get; set; } = 0;

        public string SettledBy { get; set; } = string.Empty;

        [Navigate(NavigateType.OneToMany, nameof(OrderItem.OrderId), nameof(Id))]
        public List<OrderItem> Items { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string? RoundResult { get; set; }

    }
}
