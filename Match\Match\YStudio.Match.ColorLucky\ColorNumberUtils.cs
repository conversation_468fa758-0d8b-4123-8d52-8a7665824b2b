﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.ColorLucky
{
    public class ColorNumberUtils
    {

        // 缓存字典，线程安全
        private static readonly ConcurrentDictionary<string, List<List<int>>> _combinationCache = new();

        //随机
        private static Random _random = new Random();

        /// <summary>
        /// 生成指定数量的唯一随机整数（范围可自定义）
        /// </summary>
        /// <param name="count"></param>
        /// <returns></returns>
        public static List<int> GenerateUniqueRandomNumbers(int count)
        {
            HashSet<int> uniqueNumbers = new HashSet<int>();
            while (uniqueNumbers.Count < count)
            {
                int number = _random.Next(1, 80); // 数字范围：1 ~ 98
                uniqueNumbers.Add(number);
            }
            return new List<int>(uniqueNumbers);
        }


        /// <summary>
        /// 获取指定集合中所有长度在 min 和 max 之间的组合。
        /// </summary>
        /// <param name="list"></param>
        /// <param name="min"></param>
        /// <param name="max"></param>
        /// <returns></returns>
        public static IEnumerable<List<int>> GetCombinations(List<int> list, int min, int max)
        {
            if (list == null) throw new ArgumentNullException(nameof(list));
            if (min < 1 || max < min || max > list.Count) yield break;
            for (int k = min; k <= max; k++)
            {
                foreach (var combination in GetCombinations(list, k))
                {
                    yield return combination;
                }
            }
        }


        /// <summary>
        /// 获取指定集合中所有长度为 k 的组合（带缓存）
        /// </summary>
        /// <param name="list">源集合</param>
        /// <param name="k">组合长度</param>
        /// <returns>所有组合的列表</returns>
        public static IEnumerable<List<int>> GetCombinations(List<int> list, int k)
        {
            if (list == null) throw new ArgumentNullException(nameof(list));
            int n = list.Count;
            if (k > n || k <= 0) yield break;

            // 生成唯一key（排序后保证相同内容顺序无关）
            var key = $"{string.Join(",", list.OrderBy(x => x))}|{k}";

            if (!_combinationCache.TryGetValue(key, out var cached))
            {
                var combinations = new List<List<int>>();
                int[] indices = new int[k];
                for (int i = 0; i < k; i++)
                    indices[i] = i;

                while (true)
                {
                    var current = new List<int>(k);
                    for (int i = 0; i < k; i++)
                        current.Add(list[indices[i]]);
                    combinations.Add(current.OrderBy(x => x).ToList());

                    int iPos = k - 1;
                    while (iPos >= 0 && indices[iPos] == iPos + n - k)
                        iPos--;

                    if (iPos < 0) break;

                    indices[iPos]++;
                    for (int j = iPos + 1; j < k; j++)
                        indices[j] = indices[j - 1] + 1;
                }
                _combinationCache[key] = combinations;
                cached = combinations;
            }

            foreach (var item in cached)
                yield return item;
        }


    }
}
