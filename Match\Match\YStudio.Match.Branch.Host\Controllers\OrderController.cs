﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Domain;
using YStudio.Match.Domain.Vo.Report;

namespace YStudio.Match.Branch.Host.Controllers
{

    /// <summary>
    /// 订单信息
    /// </summary>
    public class OrderController : ApiControllerBase
    {
        [Autowired]
        private readonly OrderService orderService;

        [Autowired]
        private readonly ReportService reportService;

        [Autowired]
        private readonly ICurrentUserService currentUserService;

        /// <summary>
        /// 下单
        /// </summary>
        /// <param name="order"></param>
        [HttpPost]
        public async Task<ResultBean<Order>> Submit([FromBody] Order order)
        {
            using (await AsyncLocker.OrderOperationLocker.LockAsync())
            {
                var result = await orderService.AddAsync(order);
                return ResultBean<Order>.Send(result);
            }
        }

        [HttpPost]
        public async Task<ResultBean<Order>> Cancel([FromBody] Order order)
        {
            using (await AsyncLocker.OrderOperationLocker.LockAsync())
            {
                order.BranchId = currentUserService.BranchId;
                var result = await orderService.CancelAsync(order);
                return ResultBean<Order>.Success(result);
            }

        }

        [HttpPost]
        public async Task<ResultBean<Order>> Cash([FromBody] Order order)
        {
            using (await AsyncLocker.OrderOperationLocker.LockAsync())
            {
                order.BranchId = currentUserService.BranchId;
                var result = await orderService.CashAsync(order);
                return ResultBean<Order>.Success(result);
            }
        }

        [HttpPost]
        public Order Print()
        {
            return new Order();
        }

        [HttpGet]
        public async Task<ResultBean<Order>> Query([FromQuery] Guid id)
        {
            var result = await orderService.GetByIdAsync(id);
            return ResultBean<Order>.Send(result);
        }

        [HttpGet]
        public async Task<ResultBean<Order>> GetByOrderNo([FromQuery] string orderNo)
        {
            var result = await orderService.GetByOrderNoAsync(orderNo);
            return ResultBean<Order>.Send(result);
        }

        [HttpPost]
        public async Task<PaginatedList<Order>> Fetch([FromBody] OrderQueryDto request)
        {
            request.BranchId = currentUserService.BranchId;
            if (!request.CashierId.HasValue)
            {
                request.CashierId = currentUserService.UserId;
            }

            if (request.EndDate.HasValue)
            {
                request.EndDate = request.EndDate.Value.AddDays(1).AddSeconds(-1);
            }

            return await orderService.GetPaginatedListAsync(request);
        }

        [HttpPost]
        public ResultBean<MatchReportVo> Report([FromBody] ReportQueryDto reportQueryDto)
        {
            reportQueryDto.BranchId = currentUserService.BranchId;
            reportQueryDto.AgentId = null;

            var list = reportService.Query(reportQueryDto);

            var result = list.Last();
            result.Profit = result.TotalStake - result.TotalPaid;
            result.ProfitRate = result.TotalStake == 0 ? 0 : (result.TotalStake - result.TotalPaid) / result.TotalStake;

            return ResultBean<MatchReportVo>.Send(result);
        }
    }
}
