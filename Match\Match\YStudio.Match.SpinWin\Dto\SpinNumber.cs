﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.SpinWin
{
    public class SpinNumber
    {
        /// <summary>
        /// 数字
        /// </summary>
        public int Number { get; set; }

        /// <summary>
        /// 颜色
        /// </summary>
        public EColor Color { get; set; }

        /// <summary>
        /// 字母部门
        /// </summary>
        public ELetterSector? Sector { get; set; }

        /// <summary>
        /// 数字范围 12个一打
        /// </summary>
        public EDozen? Dozens { get; set; }

        /// <summary>
        /// 是否偶数 0 既不是奇数也不是偶数 返回null
        /// </summary>
        public bool? Even { get; set; }

        /// <summary>
        /// 大小
        /// </summary>
        public ESize? Size { get; set; }

        public SpinNumber(int number, EColor color, bool? even, ESize? size, EDozen? dozens, ELetterSector? sector )
        {
            Number = number;
            Color = color;
            Sector = sector;
            Dozens = dozens;
            Even = even;
            Size = size;
        }
    }

}
