<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="System\**" />
    <EmbeddedResource Remove="System\**" />
    <None Remove="System\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="NAutowired.Core" Version="2.3.1" />
    <PackageReference Include="Nito.AsyncEx" Version="5.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\YStudio.Match.CarsRacing\YStudio.Match.CarsRacing.csproj" />
    <ProjectReference Include="..\YStudio.Match.ColorLucky\YStudio.Match.ColorLucky.csproj" />
    <ProjectReference Include="..\YStudio.Match.Domain\YStudio.Match.Domain.csproj" />
    <ProjectReference Include="..\YStudio.Match.Repository\YStudio.Match.Repository.csproj" />
    <ProjectReference Include="..\YStudio.Match.SpinWin\YStudio.Match.SpinWin.csproj" />
  </ItemGroup>

</Project>
