﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.CoreService.WebSocket.Processor
{

    [Component]
    public class PingCommandProcessor : ICommandProcessor<PingData>
    {
        public Task ProcessAsync(WebSocketSession session, PingData? data)
        {
            var serverTimestamp = DateTimeUtils.GetCurrentTimestampMilliseconds();
            var res = new PongData
            {
                ClientTimestamp = data.ClientTimestamp ?? serverTimestamp,
                ServerTimestamp = serverTimestamp
            };
            session.SendTextAsync(JsonUtils.SerializeObject(res));
            return Task.CompletedTask;
        }
    }

    public class PingData
    {
        public string? Command { get; set; } = ECommand.Ping;
        public long? ClientTimestamp { get; set; }
    }

    public class PongData : PingData
    {
        public string? Command { get; set; } = ECommand.Pong;
        public long ClientTimestamp { get; set; }
        public long ServerTimestamp { get; set; }
    }


}
