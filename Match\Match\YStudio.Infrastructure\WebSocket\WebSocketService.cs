﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;

namespace YStudio.Infrastructure.WebSocket
{
    public class WebSocketService
    {
        private readonly WebSocketSessionManager _sessionManager;

        private readonly IPAddress _address;
        private readonly int _port;
        private readonly ILogger _logger;


        private WebSocketServer? _server;

        public WebSocketService(IPAddress address,
            int port,
            WebSocketSessionManager sessionManager,
            ILogger logger)
        {
            _address = address;
            _port = port;
            _sessionManager = sessionManager;
            _logger = logger;
        }
        public void Start()
        {
            _server = new WebSocketServer(_address, _port, _sessionManager, _logger);
            _server.Start();
        }

        public void Stop()
        {
            _server?.Stop();
        }

        public void Broadcast(string message, string? group)
        {
            _server.MulticastText(message);
        }

        public bool SendText(WebSocketSession session, string message)
        {
            return session.SendTextAsync(message);
        }

        public void Dispose()
        {
            _server.Dispose();
        }
    }
}
