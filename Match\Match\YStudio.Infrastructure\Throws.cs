﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class Throws
    {


        public static void IfNullOrEmpty(object obj, string errorMessage)
        {
            if (obj is string value && string.IsNullOrEmpty(value.Trim()))
            {
                throw new ValidateException(errorMessage);
            }

            if (obj == null)
            {
                throw new ValidateException(errorMessage);
            }
        }

        public static void IfNullOrEmpty<T>(IList<T>? obj, string errorMessage)
        {
            if (obj == null)
            {
                throw new ValidateException(errorMessage);
            }

            if (obj.Count == 0)
            {
                throw new ValidateException(errorMessage);
            }
        }



        public static void IfTrue(bool? value, string errorMessage)
        {
            if (value.HasValue && value.Value) throw new ValidateException(errorMessage);
        }
        public static void IfFalse(bool? value, string errorMessage)
        {
            IfTrue(!value, errorMessage);
        }

        public static void If(bool v1, string v2)
        {
            IfTrue(v1, v2);
        }
    }
}
