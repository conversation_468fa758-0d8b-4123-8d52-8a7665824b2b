﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    public class AccountDto
    {
        public int Id { get; set; }

        public int BranchId { get; set; }

        public string BranchName { get; set; }

        public string? NickName { get; set; }

        public string? LoginAccount { get; set; }

        public DateTime? LoginAt { get; set; }

        public string? LastLoginIp { get; set; }

        public string? Token { get; set; }

        public DateTime? ExpireAt { get; set; }

        public string? accountType {  get; set; }

        public bool? isAdmin { get; set; }
    }
}
