﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace YStudio.Infrastructure
{
    public class PaginatedRequest : Dictionary<string, object>
    {
        public int PageIndex
        {
            get
            {
                return Value<int>("pageIndex", 1);
            }
        }
        public int PageSize
        {
            get
            {
                return Value<int>("pageSize", 15);
            }
        }

        public string OrderBy
        {
            get
            {
                if (string.IsNullOrEmpty(Value<string>("orderBy")))
                    return null;
                return string.Format("{0} {1}", Value<string>("orderBy"), Desc ? "DESC" : "ASC");
            }
        }

        public bool Desc
        {
            get
            {
                return Value<bool>("desc");
            }
        }

        public string? Keywords
        {
            get
            {
                return Value<string?>("keywords");
            }
        }


        [JsonIgnore]
        private JObject? obj;

        public T? Value<T>(string key, T? value = default)
        {
            try
            {
                if (obj == null)
                {
                    obj = JObject.FromObject(this);
                }

                if (!obj.ContainsKey(key))
                {
                    return value;
                }

                if (obj[key]?.GetType() == typeof(JArray))
                {
                    return obj[key].ToObject<T>();
                }

                return obj.Value<T>(key);
            }
            catch
            {
                return value;
            }
        }

        public T? ToObject<T>()
        {
            if (obj == null)
            {
                obj = JObject.FromObject(this);
            }
            return obj.ToObject<T>();
        }
    }
}
