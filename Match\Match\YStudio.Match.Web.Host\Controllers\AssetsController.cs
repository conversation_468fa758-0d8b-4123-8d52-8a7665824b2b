﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Domain;
using YStudio.Match.Domain.Enums;
using YStudio.Match.Repository;
using YStudio.Match.Web.Host.Controllers;
using YStudio.Match.Web.Host.Services;

namespace YStudio.Match.Web.Host.Controllers
{
    public class AssetsController : ApiControllerBase
    {

        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly ICurrentUserService currentUserService;


        [HttpPost]
        public async Task<ResultBean<UserAssetsLog>> Transfer([FromBody] UserAssetsLog log)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");

            using (var uow = dbContext.CreateUnitOfWork(true))
            {

                var fromUser = await dbContext.Accounts.AsQueryable()
                    .Includes(a => a.Assets)
                    .Where(a => a.Id == currentUserService.UserId)
                    .FirstAsync();

                Throws.IfNullOrEmpty(fromUser, "转出用户信息不存在");

                if (fromUser.AccountType == EAccountType.Agent)
                {
                    Throws.If(fromUser.Assets == null || fromUser.Assets.Balance < log.Balance, "转出用户余额不足");

                    fromUser.Assets.Balance -= log.Balance;
                    dbContext.UserAssets.AsUpdateable(fromUser.Assets).ExecuteCommand();
                }

                var toUser = dbContext.UserAssets.AsQueryable().Where(a => a.AccountId == log.ToId).First();
                Throws.IfNullOrEmpty(toUser, "接收用户资产信息不存在");

                log.FromId = fromUser.Id;

                toUser.Balance += log.Balance;


                dbContext.UserAssets.AsUpdateable(toUser).ExecuteCommand();
                dbContext.UserAssetsLogs.AsInsertable(log).ExecuteCommand();

                uow.Commit();
            }
            return ResultBean<UserAssetsLog>.Send(log);
        }


        [HttpPost]
        public async Task<ResultBean<UserAssetsLog>> Pay([FromBody] UserAssetsLog log)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");
            log.Status = ENormalStatus.Normal;
            await dbContext.UserAssetsLogs.AsUpdateable(log).UpdateColumns(l => new { l.Status }).ExecuteCommandAsync();
            return ResultBean<UserAssetsLog>.Send(log);
        }


        [HttpPost]
        public async Task<PaginatedList<UserAssetsLog>> Fetch([FromBody] AssetsQueryDto query)
        {

            if (currentUserService.AccountType == (int)EAccountType.Agent)
            {
                query.FromId = currentUserService.UserId;
            }

            if (query.EndDate.HasValue)
            {
                query.EndDate = query.EndDate.Value.AddDays(1).AddSeconds(-1);
            }


            var res = await dbContext.UserAssetsLogs.AsQueryable()
                 .Includes(x => x.FromAccount, a => a.Branch)
                 .Includes(x => x.ToAccount, a => a.Branch)
                 .Includes(x => x.Agent)
                 .WhereIF(query.FromId.HasValue, x => x.FromId == query.FromId)
                 .WhereIF(!string.IsNullOrEmpty(query.FromAccount), x => x.FromAccount.NickName.Contains(query.FromAccount))
                 .WhereIF(!string.IsNullOrEmpty(query.AgentName), x => x.Agent.NickName.Contains(query.AgentName))
                 .WhereIF(!string.IsNullOrEmpty(query.ToAccount), x => x.ToAccount.NickName.Contains(query.ToAccount))
                 .WhereIF(!string.IsNullOrEmpty(query.ToBranchName), x => x.ToAccount.Branch.BranchName.Contains(query.ToBranchName))
                 .WhereIF(!string.IsNullOrEmpty(query.Remark), x => x.Remark.Contains(query.Remark))
                 .WhereIF(query.StartDate.HasValue, o => o.CreateTime >= query.StartDate)
                 .WhereIF(query.EndDate.HasValue, o => o.CreateTime <= query.EndDate)
                 .OrderByDescending(x => x.CreateTime)
                 .PaginatedListAsync(query.PageIndex, query.PageSize);
            return res;
        }
    }
}
