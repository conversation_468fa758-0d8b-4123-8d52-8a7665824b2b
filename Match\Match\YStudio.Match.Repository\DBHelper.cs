﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure;

namespace YStudio.Match.Repository
{
    public static class DBHelper
    {
        //public static SqlSugarScope DbSession = new SqlSugarScope(new ConnectionConfig()
        //{
        //    ConnectionString = App.Configuration["ConnectionStrings:DefaultConnection"],//连接符字串
        //    DbType = DbType.MySql,
        //    IsAutoCloseConnection = true
        //}, db =>
        //{
        //    db.Aop.OnLogExecuting = (sql, pars) =>
        //    {
        //        // Console.WriteLine(sql);//输出sql,查看执行sql
        //        //5.0.8.2 获取无参数化 SQL 
        //        //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)

        //        Console.WriteLine(UtilMethods.GetSqlString(DbType.MySql, sql, pars));
        //    };

        //    db.Aop.DataExecuting = (oldValue, entityInfo) =>
        //    {

        //        if (entityInfo.PropertyName == "CreateBy" && entityInfo.OperationType == DataFilterType.InsertByObject)
        //        {
        //            entityInfo.SetValue("WMS_AUTO");//修改CreateBy字段

        //            /*entityInfo有字段所有参数*/

        //            /*oldValue表示当前字段值 等同于下面写法*/
        //            //var value=entityInfo.EntityColumnInfo.PropertyInfo.GetValue(entityInfo.EntityValue);

        //            /*获取当前列特性*/
        //            //5.1.3.23 +
        //            //entityInfo.IsAnyAttribute<特性>()
        //            //entityInfo.GetAttribute<特性>()
        //        }

        //    };
        //});


    }
}
