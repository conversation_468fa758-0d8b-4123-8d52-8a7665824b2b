﻿using System.Security.Claims;
using YStudio.Infrastructure;

namespace YStudio.Match.Branch.Host.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        [Autowired]
        private readonly IHttpContextAccessor httpContextAccessor;

        public int UserId
        {
            get
            {
                var val = httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == ClaimTypes.NameIdentifier).First().Value;
                return int.Parse(val);
            }
        }

        public string RemoteIP
        {
            get
            {
                var remoteIp = httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress;
                if (remoteIp == null) return string.Empty;

                if (remoteIp.IsIPv4MappedToIPv6)
                    remoteIp = remoteIp.MapToIPv4();
                return remoteIp.ToString();
            }
        }

        public int BranchId
        {
            get
            {
                return int.Parse(httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == "BranchId").First().Value);
            }
        }

        public string BranchName
        {
            get
            {
                return httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == "BranchName").First().Value;
            }
        }

        public string NickName
        {
            get
            {
                return httpContextAccessor.HttpContext.User.Claims.Where(x => x.Type == ClaimTypes.Name).First().Value;
            }
        }

        public int AccountType
        {
            get
            {
                return 0;
            }
        }
    }
}
