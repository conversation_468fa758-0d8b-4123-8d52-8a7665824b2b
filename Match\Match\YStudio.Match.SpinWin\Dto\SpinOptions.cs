﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.SpinWin
{
    public class SpinOption
    {
        public int OptionId { get; set; }

        public string? OptionName { get; set; }

        public decimal Odds { get; set; }

        public IList<int> Numbers { get; set; } = new List<int>();

        public SpinOption(int optionId, string? optionName, decimal odds, IList<int> numbers)
        {
            OptionId = optionId;
            OptionName = optionName;
            Odds = odds;
            Numbers = numbers;
        }
    }
}
