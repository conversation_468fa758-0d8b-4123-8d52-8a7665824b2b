﻿using FreeRedis;
using FreeScheduler;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;
using YStudio.Match.CarsRacing;
using YStudio.Match.ColorLucky;
using YStudio.Match.Domain.Enums;

namespace YStudio.Match.CoreService
{

    [Component]
    public class ColorLuckyRoundProcessor : IScheduleTaskHandler
    {

        [Autowired]
        protected readonly DbContext dbContext;

        [Autowired]
        protected readonly MatchService matchService;

        [Autowired]
        protected readonly OrderService orderService;

        [Autowired]
        protected readonly WebSocketSessionManager sessionManager;

        [Autowired]
        protected readonly ILogger<ColorLuckyRoundProcessor> logger;

        public async Task OnExecutingAsync(TaskInfo task)
        {
            using (await AsyncLocker.ColorLuckyRoundLocker.LockAsync())
            {
                try
                {
                    await ExecuteRoundAsync(task);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "ColorLuckyRoundProcessor OnExecutingAsync error");
                }
            }
        }
        public async Task ExecuteRoundAsync(TaskInfo task)
        {
            string[] arr = task.Body.Split("_");
            int matchId = int.Parse(arr[0]);
            string date = arr[1];
            int round = int.Parse(arr[2]);
            Guid roundId = Guid.Parse(arr[3]);

            var pendingRound = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.Id == roundId)
                .Where(r => r.Status == ERoundStatus.Pending)
                .FirstAsync();

            if (pendingRound == null)
            {
                logger.LogError("ColorLuckyRoundProcessor OnExecutingAsync: Round {RoundId} is not pending or does not exist.", roundId);
                return;
            }

            var now = DateTime.Now;

            // 查询指定比赛和轮次的订单及其订单项 
            var orderItems = await orderService.GetOrderItemsAsync(matchId, roundId);

            //从数据库读取
            var ruleConfig = await GetRuleMapAsync();

            var orders = orderItems.Where(oi => oi.OptionType == "NUMBER").Select(oi => new ColorLucky.OrderItem
            {
                Numbers = oi.OptionName.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(int.Parse).ToList(),
                Stake = oi.Stake,
                Payout = oi.MaxPayout,
                Type = oi.OptionType == "NUMBER" ? 0 : 1
            }).Distinct().ToList();

            //20个数字开奖
            ColorLuckyHandler colorLuckyHandler = new ColorLuckyHandler();
            var numResult = colorLuckyHandler.Handle(orders, ruleConfig);

            //记录结算日志
            await dbContext.MatchRoundLogs.AsInsertable(new MatchRoundLog
            {
                MatchId = matchId,
                RoundId = pendingRound.Id,
                Round = pendingRound.Round,
                EndTime = pendingRound.EndTime,
                LogMessage = colorLuckyHandler.LogMessage.ToString()
            }).ExecuteCommandAsync();

            //1.更新结果到Round表
            await dbContext.MatchRounds.AsUpdateable(new MatchRound() { Id = roundId, Result = JsonUtils.SerializeObject(numResult), Status = ERoundStatus.Completed }).UpdateColumns(m => new { m.Result, m.Status }).ExecuteCommandAsync();

            //2.获取最新期数数据
            var recents = await matchService.GetRecentRoundsAsync(matchId, 3);
            // 获取下一期数据
            var nextRound = await matchService.GetNextRoundAsync(matchId, now);

            //当执行时间距离开奖时间小于10秒时，进行广播
            if ((now - pendingRound.EndTime) < TimeSpan.FromSeconds(10))
            {
                //3.广播结果
                BraodcastResult(matchId, round, now, nextRound, recents, numResult);
            }
            //4.更新订单信息

            //颜色最多
            var winColor = colorLuckyHandler.GetWinColorByNumbers(numResult);


            //2.处理所有订单项的状态
            orderItems.ForEach(oi =>
            {
                oi.Status = EOrderState.Lose;

                if (oi.OptionType == "NUMBER")
                {
                    IList<int> orderNumbers = oi.OptionName.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(int.Parse).ToList();

                    int winCount = colorLuckyHandler.GetWinCount(numResult, orderNumbers);
                    if (winCount >= 3)
                    {
                        decimal odds = colorLuckyHandler.GetOddsByOption(orderNumbers.Count, winCount);
                        oi.Status = EOrderState.Win;
                        oi.ActualPayout = oi.Stake * odds;
                        oi.Odds = odds;
                    }
                }

                if (oi.OptionType == "COLOR" && winColor != null)
                {
                    if (oi.OptionName == winColor.ToString())
                    {
                        oi.Status = EOrderState.Win;
                        oi.ActualPayout = oi.MaxPayout;
                    }
                }
            });



            using (var uow = dbContext.CreateUnitOfWork())
            {
                //更新所有订单item
                await dbContext.OrderItems.UpdateRangeAsync(orderItems);
                // 3.更新订单总状态
                await orderService.UpdateOrderStatusByRound(roundId);
                uow.Commit();
            }
        }


        /// <summary>
        /// 读取配置
        /// </summary>
        /// <returns></returns>
        private async Task<Dictionary<string, bool>> GetRuleMapAsync()
        {
            Dictionary<string, bool> ruleConfig = new Dictionary<string, bool> {
                { "8_8", false },
                { "8_7", false },
                { "7_7", false },
                { "7_6", false },
                { "6_6", false },
                { "6_5", false },
                { "5_5", false }
            };

            var config = await dbContext.Configs.AsQueryable().Where(c => c.ConfigKey == EConfigKey.ColorLucky).FirstAsync();
            if (config != null && !string.IsNullOrEmpty(config.ConfigValue))
            {
                try
                {
                    var setting = JsonConvert.DeserializeObject<Dictionary<string, bool>>(config.ConfigValue);
                    if (setting != null)
                    {
                        ruleConfig = setting;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "ColorLuckyRoundProcessor OnExecutingAsync: Error parsing ColorLucky config.");
                }
            }

            return ruleConfig;
        }

        private void BraodcastResult(int matchId, int round, DateTime now, MatchRound nextRound, IList<MatchRound> recents, List<int> numResult)
        {
            var result = new ColorLuckyResult
            {
                Command = ECommand.ColorLuckyRoundResult,
                MatchId = matchId,
                CurrentRound = round,
                CurrentTime = DateTimeUtils.ToTimestampMilliseconds(now),
                NextRound = nextRound.Round,
                NextTime = DateTimeUtils.ToTimestampMilliseconds(nextRound.EndTime),
                CurrentNumber = numResult,
                RecentResult = recents.Select(item => new ColorLuckyRoundResult
                {
                    Round = item.Round,
                    RoundTime = DateTimeUtils.ToTimestampMilliseconds(item.EndTime),
                    Result = JsonConvert.DeserializeObject<IList<int>>(item.Result),
                }).ToList()
            };
            // 发送结果到所有订阅的客户端
            sessionManager.BroadcastToGroup(matchId.ToString(), JsonUtils.SerializeObject(result));
        }
    }
}
