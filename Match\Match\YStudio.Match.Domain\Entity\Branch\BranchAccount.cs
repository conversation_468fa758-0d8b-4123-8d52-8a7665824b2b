﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{

    [SugarTable("t_branch_account")]
    public class BranchAccount2 : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public int BranchId { get; set; }

        public string? NickName { get; set; }

        public string? LoginAccount { get; set; }

        public string? LoginPassword { get; set; }

        public string? Salt { get; set; }
        public bool IsAdmin { get; set; }

        public DateTime? LoginAt { get; set; }

        public string? LastLoginIp { get; set; }

        [Navigate(NavigateType.OneToOne, nameof(BranchId), nameof(Domain.Branch.Id))]
        public Branch Branch { get; set; }
    }
}
