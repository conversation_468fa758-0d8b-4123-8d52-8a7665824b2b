﻿using ESCPOS;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using YStudio.Match.PrinterService.Domain;
using Newtonsoft.Json;
using YStudio.Match.PrinterService.Utils;

namespace YStudio.Match.PrinterService
{
    public partial class Form1 : Form
    {

        public string PrintePort { get; set; } = "";

        private IntPtr Printer;

        private OrderPrinterHelper OrderPrinter;

        private EConnectType ConnectType { get; set; } = 0; //0:USB, 1:COM, 2:NET

        private int ConnectStatus { get; set; } = 0;  //0:连接成功, 

        public Form1()
        {
            InitializeComponent();

            Printer = XPrinter.InitPrinter("");

            OrderPrinter = new OrderPrinterHelper();

            InitHttpServer();

            InitializeData();
        }

        private void InitHttpServer()
        {
            try
            {
                //初始化HTTP服务器
                HttpServer server = new HttpServer();
                server.AddRoute("/print", this.PrinterHandler);
                server.Start();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"HTTP Server Init Error : {ex.Message}", "ERROR");
                Close();
            }
        }
        private async Task<string> PrinterHandler(HttpListenerContext context)
        {
            //获取请求方法
            string method = context.Request.HttpMethod.ToUpperInvariant();
            if (method == "POST")
            {
                //处理打印文本和切纸请求
                return await PrinterTextAndCut(context);
            }
            else
            {
                //返回错误信息
                context.Response.StatusCode = (int)HttpStatusCode.MethodNotAllowed;
                return "Method not allowed";
            }
        }

        private async Task<string> PrinterTextAndCut(HttpListenerContext context)
        {

            if (ConnectStatus != 0)
            {
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return "Printer is not connected or connection failed";
            }


            //获取请求体
            using (var reader = new StreamReader(context.Request.InputStream, context.Request.ContentEncoding))
            {
                string requestBody = await reader.ReadToEndAsync();
                if (string.IsNullOrEmpty(requestBody))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    return "Request body is empty";
                }

                var order = JsonUtils.Deserialize<OrderModel>(requestBody);

                if (order is null || string.IsNullOrEmpty(order.OrderNo) || order.Items.Count == 0)
                {
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    return "Invalid order data";
                }

                OrderPrinter.AddOrder(order);
                var printerStatus = OrderPrinter.Print(Printer);

                if (printerStatus != 0)
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    return $"Printer error: {printerStatus}";
                }

                context.Response.StatusCode = (int)HttpStatusCode.OK;
                return await Task.FromResult("success");
            }
        }

        private void InitializeData()
        {
            string[] ports = SerialPort.GetPortNames();
            foreach (var item in ports)
            {
                cmbPort.Items.Add(item);
            }
            try
            {
                if (!File.Exists("config.dat"))
                {
                    return;
                }
                var line = File.ReadAllText("config.dat");

                if (!string.IsNullOrEmpty(line))
                {
                    string[] items = line.Split('|');
                    ConnectType = (EConnectType)Enum.Parse(typeof(EConnectType), items[0]);
                    cmbPort.Text = items[1];
                    cmbBaudrate.Text = items[2];
                    txtIPaddress.Text = items[3];

                    if (ConnectType == EConnectType.USB)
                    {
                        radioUsb.Checked = true;
                    }
                    else if (ConnectType == EConnectType.COM)
                    {
                        radioCom.Checked = true;
                    }
                    else if (ConnectType == EConnectType.NET)
                    {
                        radioNet.Checked = true;
                    }

                    this.btnOpen_Click(null, null); //尝试打开连接
                }

                chkAutoStart.Checked = AutoStartUtils.IsAutoStartEnabled();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error reading config file: {ex.Message}", "ERROR");
            }
        }
        private void Form1_FormClosed(object sender, FormClosedEventArgs e)
        {
            XPrinter.ReleasePrinter(Printer);
        }

        private void radioUsb_CheckedChanged(object sender, EventArgs e)
        {
            ConnectType = EConnectType.USB;
            cmbPort.Enabled = false;
            cmbBaudrate.Enabled = false;
            txtIPaddress.Enabled = false;
        }

        private void radioCom_CheckedChanged(object sender, EventArgs e)
        {
            ConnectType = EConnectType.COM;
            cmbPort.Enabled = true;
            cmbBaudrate.Enabled = true;
            txtIPaddress.Enabled = false;
        }

        private void radioNet_CheckedChanged(object sender, EventArgs e)
        {
            ConnectType = EConnectType.NET;
            cmbPort.Enabled = false;
            cmbBaudrate.Enabled = false;
            txtIPaddress.Enabled = true;
        }

        private void btnOpen_Click(object sender, EventArgs e)
        {
            if (ConnectStatus == 0)
            {
                XPrinter.ClosePort(Printer);
            }

            if (ConnectType == EConnectType.USB)
            {
                ConnectStatus = XPrinter.OpenPort(Printer, "USB,");
            }
            else if (ConnectType == EConnectType.COM)
            {
                ConnectStatus = XPrinter.OpenPort(Printer, $"{cmbPort.Text},{cmbBaudrate.Text}");
            }
            else if (ConnectType == EConnectType.NET)
            {
                ConnectStatus = XPrinter.OpenPort(Printer, $"NET,{txtIPaddress.Text}");
            }

            if (ConnectStatus != 0)
            {
                MessageBox.Show("Fail to connect printer, please check!", "ERROR");
            }
            else
            {
                btnOpen.Enabled = false;
                btnPrintTest.Enabled = true;
                btnClose.Enabled = true;
                string line = $"{ConnectType}|{cmbPort.Text}|{cmbBaudrate.Text}|{txtIPaddress.Text}";
                File.WriteAllText("config.dat", line);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            if (ConnectStatus == 0)
            {
                XPrinter.ClosePort(Printer);
                //标记为未连接
                ConnectStatus = -1;
            }

            if (ConnectType == EConnectType.USB)
            {
                btnClose.Enabled = false;
            }
            else if (ConnectType == EConnectType.COM)
            {
                cmbPort.Enabled = false;
                cmbBaudrate.Enabled = false;
            }
            else if (ConnectType == EConnectType.NET)
            {
                txtIPaddress.Enabled = true;
            }

            btnPrintTest.Enabled = false;
            btnClose.Enabled = false;
            btnOpen.Enabled = true;
        }

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            //点击X按钮时，提示最小化到右下角
            if (this.WindowState != FormWindowState.Minimized)
            {
                e.Cancel = true; //取消关闭事件
                this.WindowState = FormWindowState.Minimized; //最小化窗口
                ShowInTaskbar = false; //隐藏任务栏图标
                notifyIcon.Visible = true; //显示托盘图标
                notifyIcon.BalloonTipText = "Printer Service is running in the background. Click to restore.";
                notifyIcon.ShowBalloonTip(3000, "Tips", "Printer Service is running in the background!", ToolTipIcon.Info); //显示气泡提示
            }
        }

        private void notifyIcon_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                //左键点击时，显示窗口
                this.ShowInTaskbar = true; //显示任务栏图标
                this.WindowState = FormWindowState.Normal; //恢复窗口状态
                this.Activate(); //激活窗口
                this.Focus();
            }
            else if (e.Button == MouseButtons.Right)
            {
                //右键点击时，显示上下文菜单
                contextMenuStrip.Show(Cursor.Position);
            }
        }

        private void onExit_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void Form1_Shown(object sender, EventArgs e)
        {

        }

        private void btnPrintTest_Click(object sender, EventArgs e)
        {
            var order = new OrderModel
            {
                Title = "MAGIC ORDER[TEST]中文",
                BranchName = "Test Branch",
                Cashier = "Test Cashier中文",
                OrderNo = "00000000000000",
                MatchName = "Test Match",
                CreateTime = DateTime.Now,
                Round = 000,
                Stake = 0.00m,
                MaxPayout = 0.00m,
                Items = new List<OrderItemModel>
                {
                    new OrderItemModel { OptionName = "0",OptionType="0", Stake = 0.00m, MaxPayout = 0.00m, Odds=0.00m},
                    new OrderItemModel { OptionName = "0",OptionType="0", Stake = 0.00m, MaxPayout = 0.00m, Odds=0.00m},
                }
            };
            OrderPrinter.AddOrder(order);
            OrderPrinter.Print(Printer);
        }

        private void chkAutoStart_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                AutoStartUtils.SetAutoStart(chkAutoStart.Checked);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Set Auto Start Error Message:" + ex.Message, "Error");
            }
        }
    }
}
