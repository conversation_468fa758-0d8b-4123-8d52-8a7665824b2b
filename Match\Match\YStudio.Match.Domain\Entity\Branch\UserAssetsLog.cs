﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    [SugarTable("t_user_assets")]
    public class UserAssets : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public int? AccountId { get; set; }
        public int? BranchId { get; set; }
        public decimal? Balance { get; set; }
        public decimal? Freeze { get; set; }
    }
}
