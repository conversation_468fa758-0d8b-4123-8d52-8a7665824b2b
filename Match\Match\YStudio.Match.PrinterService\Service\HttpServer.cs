﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.Remoting.Contexts;
using System.Text;
using System.Threading.Tasks;
using YStudio.Match.PrinterService.Utils;

namespace YStudio.Match.PrinterService
{
    public class HttpServer
    {
        private readonly HttpListener listener;

        private readonly Dictionary<string, Func<HttpListenerContext, Task<string>>> routes = new Dictionary<string, Func<HttpListenerContext, Task<string>>>();
        public HttpServer()
        {
            if (!HttpListener.IsSupported)
            {
                throw new NotSupportedException("Current platform not support HttpListener");
            }

            listener = new HttpListener();
            listener.Prefixes.Add("http://localhost:9999/"); // 监听本地9999端口
        }


        public void AddRoute(string route, Func<HttpListenerContext, Task<string>> handler)
        {
            if (string.IsNullOrEmpty(route) || handler == null)
            {
                throw new ArgumentException("路径和处理程序不能为空");
            }
            //添加路由映射
            routes.Add(route, handler);
        }
        public void Start()
        {
            if (!listener.IsListening)
            {
                listener.Start();
                listener.BeginGetContext(ProcessRequestCallback, listener);
            }
        }

        public void Stop()
        {
            if (listener.IsListening)
            {
                listener.Stop();
                listener.Close();
            }
        }

        //处理客户端（即外部程序）发来请求的处理逻辑 （这里是定义的回调函数）
        private async void ProcessRequestCallback(IAsyncResult result)
        {
            HttpListener listener = (HttpListener)result.AsyncState;
            //开始下一个请求的监听
            listener.BeginGetContext(ProcessRequestCallback, listener);

            HttpListenerContext context = listener.EndGetContext(result);

            //设置响应头中的 Cache-Control 为 no-cache，禁止浏览器缓存
            context.Response.Headers.Add("Cache-Control", "no-cache");
            //设置响应头中的 Access-Control-Allow-Origin 为 *，允许跨域访问
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
            //设置响应头中的 Access-Control-Allow-Methods 为 GET, POST, OPTIONS，允许的请求方法
            context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type,Authorization");

            // 处理OPTIONS预检请求
            if (context.Request.HttpMethod.ToUpperInvariant() == "OPTIONS")
            {
                context.Response.StatusCode = (int)HttpStatusCode.OK;
                context.Response.ContentLength64 = 0;
                context.Response.OutputStream.Close();
                return;
            }
            try
            {

                //获取请求方法和URL路径
                string httpMethod = context.Request.HttpMethod;
                string url = context.Request.Url.AbsolutePath;
                string responseString = "No Data!";//默认响应字符串
                Func<HttpListenerContext, Task<string>> handler;

                //如果请求路径存在与路由映射中，执行相应的处理程序
                if (routes.TryGetValue(url, out handler))
                {
                    //获取处理程序返回的响应数据
                    responseString = await handler(context);

                    if (context.Response.StatusCode != (int)HttpStatusCode.OK)
                    {
                        await ErrorHandler(context, new Exception(responseString));
                        return;
                    }

                    //将响应数据编码成字节数组
                    byte[] buffer = System.Text.Encoding.UTF8.GetBytes(JsonUtils.SerializeObject(new
                    {
                        Code = 200,
                        Message = responseString,
                    }));

                    //设置响应的内容长度和状态码
                    context.Response.ContentLength64 = buffer.Length;
                    context.Response.StatusCode = (int)HttpStatusCode.OK;

                    // 设置响应头中的 Content-Type 为 application/json 并指定字符编码为 UTF-8(修复浏览器访问GET请求时，反馈值中文乱码问题)
                    context.Response.ContentType = "application/json; charset=utf-8";

                    //将响应写入输出流并关闭输出流
                    context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                    context.Response.OutputStream.Close();

                }
                else
                {
                    //如果请求不存在与路由映射中，返回404错误
                    context.Response.StatusCode = (int)HttpStatusCode.NotFound;
                    context.Response.Close();
                }
            }
            catch (Exception ex)
            {
                await ErrorHandler(context, ex);
            }
        }


        private async Task ErrorHandler(HttpListenerContext context, Exception ex)
        {
            try
            {
                var errorContext = new { Code = 501, Message = ex.Message };
                byte[] buffer = Encoding.UTF8.GetBytes(JsonUtils.SerializeObject(errorContext));

                context.Response.StatusCode = (int)HttpStatusCode.OK;
                context.Response.ContentType = "application/json; charset=utf-8";
                context.Response.ContentLength64 = buffer.Length;
                context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                context.Response.OutputStream.Close();
                await Task.CompletedTask;
            }
            catch (Exception)
            {

            }
        }
    }
}
