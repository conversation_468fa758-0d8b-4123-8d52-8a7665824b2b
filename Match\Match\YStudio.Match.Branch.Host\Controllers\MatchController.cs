﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.Intrinsics.X86;
using YStudio.Infrastructure;

namespace YStudio.Match.Branch.Host.Controllers
{

    public class MatchController : ApiControllerBase
    {
        [Autowired]
        protected readonly MatchService matchService;


        [HttpGet]
        public async Task<ResultBean<List<MatchOption>>> Options()
        {
            var result = await matchService.GetOptionsAsync();
            return ResultBean<List<MatchOption>>.Send(result);
        }

        [HttpGet]
        public async Task<ResultBean<List<MatchRound>>> Rounds([FromQuery] int matchId)
        {
            var result = await matchService.GetRoundsAsync(matchId);
            return ResultBean<List<MatchRound>>.Send(result);
        }
    }
}
