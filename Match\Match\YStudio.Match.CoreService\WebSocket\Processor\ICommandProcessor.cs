﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YStudio.Infrastructure.WebSocket;

namespace YStudio.Match.CoreService.WebSocket.Processor
{
    public interface ICommandProcessor<T>
    {
        async Task ProcessAsync(WebSocketSession session, object data)
        {
            await ProcessAsync(session, (T)data);
        }
        Task ProcessAsync(WebSocketSession session, T data);
    }
}
