﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{

    [SugarTable("t_order_snapshot")]
    public class OrderSnapshot
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        public int BranchId { get; set; }

        public int MatchId { get; set; }

        public long Round { get; set; }

        public int OptionId { get; set; }

        public string? OptionName { get; set; }

        public decimal Stake { get; set; }

        public decimal Payout { get; set; }

        public decimal Odds { get; set; }

        public DateTime? Date { get; set; }
    }
}
