﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class BaseException : Exception
    {
        public int Code { get; set; }

        public BaseException(string message) : base(message)
        {
        }

        public BaseException(int code, string message) : base(message)
        {
            Code = code;
        }
    }
}
