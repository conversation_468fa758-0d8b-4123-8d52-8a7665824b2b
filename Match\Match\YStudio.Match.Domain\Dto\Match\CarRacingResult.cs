﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    public class CarRacingResult
    {
        public int MatchId { get; set; }
        public string Command { get; set; } = string.Empty;
        public int? CurrentRound { get; set; }
        public long CurrentTime { get; set; }
        public int NextRound { get; set; }
        public long NextTime { get; set; }
        public List<int> CurrentNumber { get; set; } = new List<int>();
        public List<CarRacingRoundResult> RecentResult { get; set; } = new List<CarRacingRoundResult>();
        public List<JackpotItem> Jackpot { get; set; } = new List<JackpotItem>();
    }
    public class JackpotItem
    {
        public string Title { get; set; } = string.Empty;
        public DateTime Time { get; set; }
        public decimal Payout { get; set; }
    }
    public class CarRacingRoundResult
    {
        public int Round { get; set; }
        public long RoundTime { get; set; }

        public IList<int> Result { get; set; } = new List<int>();
    }
}
