﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Infrastructure
{
    public class PaginatedList<T>
    {
        public List<T> Items { get; }
        public int PageIndex { get; }
        public int TotalPages { get; }
        public int TotalCount { get; }

        public int Code { get; set; } = 200;

        public PaginatedList(List<T> items, int totalCount, int pageIndex, int pageSize)
        {
            PageIndex = pageIndex;
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            TotalCount = totalCount;
            Items = items;
        }

        public PaginatedList(List<T> items, int totalCount, int pageIndex, int pageSize, int totalPages)
        {
            PageIndex = pageIndex;
            TotalPages = totalPages;
            TotalCount = totalCount;
            Items = items;
        }

        public bool HasPreviousPage => PageIndex > 1;

        public bool HasNextPage => PageIndex < TotalPages;


        public static async Task<PaginatedList<T>> CreateAsync(ISugarQueryable<T> source, int pageIndex, int pageSize, CancellationToken cancellationToken = default(CancellationToken))
        {
            RefAsync<int> totalCount = 0;
            RefAsync<int> totalPages = 0;

            var items = await source.ToPageListAsync(pageIndex, pageSize, totalCount, totalPages, cancellationToken);
            return new PaginatedList<T>(items, totalCount, pageIndex, pageSize, totalPages);
        }

        public static PaginatedList<T> Create(ISugarQueryable<T> source, int pageIndex, int pageSize, CancellationToken cancellationToken = default(CancellationToken))
        {
            int totalCount = 0;
            int totalPages = 0;

            var items = source.ToPageList(pageIndex, pageSize, ref totalCount, ref totalPages);
            return new PaginatedList<T>(items, totalCount, pageIndex, pageSize, totalPages);
        }

    }
}
