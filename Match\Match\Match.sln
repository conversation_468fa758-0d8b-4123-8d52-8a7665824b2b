﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35222.181
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{45403D32-DB41-413C-9B34-73C3E768273E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{B4437C32-D718-49EB-A859-6037FE7063F7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{0FE9FA3C-C0B4-4C5B-AF0D-7568E8E124BD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "YStudio.Match.CoreService", "YStudio.Match.CoreService\YStudio.Match.CoreService.csproj", "{106842B0-73B3-4D21-96D9-8AE02CFAAFD7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "YStudio.Match.Domain", "YStudio.Match.Domain\YStudio.Match.Domain.csproj", "{7B968098-DA5F-49F3-8FEA-9C47E9015341}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "YStudio.Match.Repository", "YStudio.Match.Repository\YStudio.Match.Repository.csproj", "{441A0A95-8B42-4BB7-BE72-FE72F3B487D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "YStudio.Infrastructure", "YStudio.Infrastructure\YStudio.Infrastructure.csproj", "{42711F2F-A513-4869-BB2F-4212BFC7758E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "YStudio.Match.Web.Host", "YStudio.Match.Web.Host\YStudio.Match.Web.Host.csproj", "{656FEA13-0A59-472A-996C-8B3CD045DA61}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "YStudio.Match.Branch.Host", "YStudio.Match.Branch.Host\YStudio.Match.Branch.Host.csproj", "{1FAB0EB1-A3E5-4412-82C6-4367FFF0F913}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YStudio.Match.SpinWin", "YStudio.Match.SpinWin\YStudio.Match.SpinWin.csproj", "{A1F25607-AC37-42F4-BC5E-6D4DEA009453}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ConsoleApp1", "ConsoleApp1\ConsoleApp1.csproj", "{BA850967-EBE2-4B74-8F16-137BC2AC7407}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YStudio.Match.CarsRacing", "YStudio.Match.CarsRacing\YStudio.Match.CarsRacing.csproj", "{09E8793E-9911-4619-86F3-C26E43266DF2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Game", "Game", "{C127A03D-8250-43ED-9714-854E4F16E37B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YStudio.Match.ColorLucky", "YStudio.Match.ColorLucky\YStudio.Match.ColorLucky.csproj", "{56E284C5-DC29-4199-B1BF-F81C99C94070}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PrinterService", "PrinterService", "{0DC5ECC8-A233-466F-9FDD-F9C7902BB4D0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YStudio.Match.PrinterService", "YStudio.Match.PrinterService\YStudio.Match.PrinterService.csproj", "{A328C7F6-0F0C-4136-A166-85FC7A6F4F5E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{AC2DFAEE-F8DD-4985-A746-6B4BB57DF994}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "BranchClient", "BranchClient", "{40F82FC3-0AAD-4114-8D03-6DCB6950E5A4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YStudio.Match.BranchClient", "YStudio.Match.BranchClient\YStudio.Match.BranchClient.csproj", "{F961C8B8-5EEC-4376-AE13-D709170FCF9F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{106842B0-73B3-4D21-96D9-8AE02CFAAFD7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{106842B0-73B3-4D21-96D9-8AE02CFAAFD7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{106842B0-73B3-4D21-96D9-8AE02CFAAFD7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{106842B0-73B3-4D21-96D9-8AE02CFAAFD7}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B968098-DA5F-49F3-8FEA-9C47E9015341}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B968098-DA5F-49F3-8FEA-9C47E9015341}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B968098-DA5F-49F3-8FEA-9C47E9015341}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B968098-DA5F-49F3-8FEA-9C47E9015341}.Release|Any CPU.Build.0 = Release|Any CPU
		{441A0A95-8B42-4BB7-BE72-FE72F3B487D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{441A0A95-8B42-4BB7-BE72-FE72F3B487D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{441A0A95-8B42-4BB7-BE72-FE72F3B487D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{441A0A95-8B42-4BB7-BE72-FE72F3B487D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{42711F2F-A513-4869-BB2F-4212BFC7758E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42711F2F-A513-4869-BB2F-4212BFC7758E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42711F2F-A513-4869-BB2F-4212BFC7758E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42711F2F-A513-4869-BB2F-4212BFC7758E}.Release|Any CPU.Build.0 = Release|Any CPU
		{656FEA13-0A59-472A-996C-8B3CD045DA61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{656FEA13-0A59-472A-996C-8B3CD045DA61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{656FEA13-0A59-472A-996C-8B3CD045DA61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{656FEA13-0A59-472A-996C-8B3CD045DA61}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FAB0EB1-A3E5-4412-82C6-4367FFF0F913}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FAB0EB1-A3E5-4412-82C6-4367FFF0F913}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FAB0EB1-A3E5-4412-82C6-4367FFF0F913}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FAB0EB1-A3E5-4412-82C6-4367FFF0F913}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1F25607-AC37-42F4-BC5E-6D4DEA009453}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1F25607-AC37-42F4-BC5E-6D4DEA009453}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1F25607-AC37-42F4-BC5E-6D4DEA009453}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1F25607-AC37-42F4-BC5E-6D4DEA009453}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA850967-EBE2-4B74-8F16-137BC2AC7407}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA850967-EBE2-4B74-8F16-137BC2AC7407}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA850967-EBE2-4B74-8F16-137BC2AC7407}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA850967-EBE2-4B74-8F16-137BC2AC7407}.Release|Any CPU.Build.0 = Release|Any CPU
		{09E8793E-9911-4619-86F3-C26E43266DF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09E8793E-9911-4619-86F3-C26E43266DF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09E8793E-9911-4619-86F3-C26E43266DF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09E8793E-9911-4619-86F3-C26E43266DF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{56E284C5-DC29-4199-B1BF-F81C99C94070}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{56E284C5-DC29-4199-B1BF-F81C99C94070}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{56E284C5-DC29-4199-B1BF-F81C99C94070}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{56E284C5-DC29-4199-B1BF-F81C99C94070}.Release|Any CPU.Build.0 = Release|Any CPU
		{A328C7F6-0F0C-4136-A166-85FC7A6F4F5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A328C7F6-0F0C-4136-A166-85FC7A6F4F5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A328C7F6-0F0C-4136-A166-85FC7A6F4F5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A328C7F6-0F0C-4136-A166-85FC7A6F4F5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F961C8B8-5EEC-4376-AE13-D709170FCF9F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F961C8B8-5EEC-4376-AE13-D709170FCF9F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F961C8B8-5EEC-4376-AE13-D709170FCF9F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F961C8B8-5EEC-4376-AE13-D709170FCF9F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{106842B0-73B3-4D21-96D9-8AE02CFAAFD7} = {45403D32-DB41-413C-9B34-73C3E768273E}
		{7B968098-DA5F-49F3-8FEA-9C47E9015341} = {45403D32-DB41-413C-9B34-73C3E768273E}
		{441A0A95-8B42-4BB7-BE72-FE72F3B487D8} = {45403D32-DB41-413C-9B34-73C3E768273E}
		{42711F2F-A513-4869-BB2F-4212BFC7758E} = {B4437C32-D718-49EB-A859-6037FE7063F7}
		{656FEA13-0A59-472A-996C-8B3CD045DA61} = {0FE9FA3C-C0B4-4C5B-AF0D-7568E8E124BD}
		{1FAB0EB1-A3E5-4412-82C6-4367FFF0F913} = {0FE9FA3C-C0B4-4C5B-AF0D-7568E8E124BD}
		{A1F25607-AC37-42F4-BC5E-6D4DEA009453} = {C127A03D-8250-43ED-9714-854E4F16E37B}
		{BA850967-EBE2-4B74-8F16-137BC2AC7407} = {0FE9FA3C-C0B4-4C5B-AF0D-7568E8E124BD}
		{09E8793E-9911-4619-86F3-C26E43266DF2} = {C127A03D-8250-43ED-9714-854E4F16E37B}
		{56E284C5-DC29-4199-B1BF-F81C99C94070} = {C127A03D-8250-43ED-9714-854E4F16E37B}
		{A328C7F6-0F0C-4136-A166-85FC7A6F4F5E} = {0DC5ECC8-A233-466F-9FDD-F9C7902BB4D0}
		{F961C8B8-5EEC-4376-AE13-D709170FCF9F} = {40F82FC3-0AAD-4114-8D03-6DCB6950E5A4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D0268339-14FF-410E-BC7F-9089B4AEC207}
	EndGlobalSection
EndGlobal
