﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.CarsRacing
{
    public class CarCombination
    {
        public int FirstNumber { get; set; }
        public int SecondNumber { get; set; }

        public List<int> Options { get; set; } = new List<int>();

        public CarCombination(int firstNumber, int secondNumber, List<int> options)
        {
            FirstNumber = firstNumber;
            SecondNumber = secondNumber;
            Options = options;
        }

        public CarCombination(int firstNumber, int secondNumber)
        {
            FirstNumber = firstNumber;
            SecondNumber = secondNumber;
        }
    }
}
