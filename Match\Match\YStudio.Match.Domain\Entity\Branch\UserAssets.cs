﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    [SugarTable("t_user_assets_log")]
    public class UserAssetsLog : AbstractEntity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public int FromId { get; set; }

        [Navigate(NavigateType.OneToOne, nameof(FromId), nameof(Account.Id))]
        public Account? FromAccount { get; set; }

        public int ToId { get; set; }

        [Navigate(NavigateType.OneToOne, nameof(ToId), nameof(Account.Id))]
        public Account? ToAccount { get; set; }


        public int AgentId { get; set; }

        [Navigate(NavigateType.OneToOne, nameof(AgentId), nameof(Account.Id))]
        public Account? Agent { get; set; }

        public decimal Balance { get; set; }

        public string Remark { get; set; } = string.Empty;

        public ENormalStatus Status { get; set; } = ENormalStatus.Disabled;
    }
}
