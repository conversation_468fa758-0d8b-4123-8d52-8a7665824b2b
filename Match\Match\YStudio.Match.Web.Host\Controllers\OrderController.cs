﻿using FreeSql;
using Microsoft.AspNetCore.Mvc;
using YStudio.Infrastructure;
using YStudio.Match.CoreService;
using YStudio.Match.Domain;
using YStudio.Match.Domain.Dto.Account;
using YStudio.Match.Domain.Enums;
using YStudio.Match.Domain.Vo.Report;
using YStudio.Match.Repository;

namespace YStudio.Match.Web.Host.Controllers
{
    public class OrderController : ApiControllerBase
    {

        [Autowired]
        private readonly OrderService orderService;

        [Autowired]
        private readonly ReportService reportService;

        [Autowired]
        private readonly ICurrentUserService currentUserService;

        [Autowired]
        protected readonly Repository.DbContext dbContext;

        [HttpPost]
        public async Task<PaginatedList<Order>> Fetch([FromBody] OrderQueryDto request)
        {

            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");

            if (request.EndDate.HasValue)
            {
                request.EndDate = request.EndDate.Value.AddDays(1).AddSeconds(-1);
            }

            if (request.AgentId.HasValue && !request.BranchId.HasValue)
            {
                //如果传了代理商ID，但没传营业网点ID，则查询该代理商下所有营业网点
                request.BranchIds = dbContext.Branchs.AsQueryable()
                    .Where(b => b.AccountId == request.AgentId)
                    .Select(b => b.Id).ToList();
            }

            if (request.BranchId.HasValue)
            {
                //如果传了营业网点ID，则忽略代理商ID
                request.BranchIds = null;
            }

            //Order中的ActualPayout是通过Items中Status是否有Win计算出来的，不能直接在数据库中查询
            var orders = await dbContext.Orders.AsQueryable()
                .Includes(o => o.Branch, b => b.Account)
                .WhereIF(!string.IsNullOrEmpty(request.OrderNo), o => o.OrderNo.Contains(request.OrderNo))
                .WhereIF(request.MatchId.HasValue, o => o.MatchId == request.MatchId)
                .WhereIF(request.Round.HasValue, o => o.Round == request.Round)
                .WhereIF(request.BranchId.HasValue, o => o.BranchId == request.BranchId)
                .WhereIF(request.BranchIds?.Count > 0, o => request.BranchIds.Contains(o.BranchId))
                .WhereIF(request.CashierId.HasValue, o => o.CashierId == request.CashierId || o.SettledId == request.CashierId)
                .WhereIF(request.StartDate.HasValue, o => o.CreateTime >= request.StartDate.Value)
                .WhereIF(request.EndDate.HasValue, o => o.CreateTime <= request.EndDate.Value)
                .OrderByDescending(o => o.CreateTime)
                .PaginatedListAsync(request.PageIndex, request.PageSize);

            return orders;
        }

        [HttpGet]
        public async Task<ResultBean<Order>> Query([FromQuery] Guid id)
        {
            Throws.IfTrue(currentUserService.AccountType != 10, "无权限");

            var order = await dbContext.Orders.AsQueryable()
             .Includes(o => o.Items)
             .Where(o => o.Id == id)
             .FirstAsync();

            Throws.IfNullOrEmpty(order, "ticket is not find");

            var round = await dbContext.MatchRounds.AsQueryable()
                .Where(r => r.Id == order.RoundId)
                .FirstAsync();

            order.RoundResult = round.Result;

            return ResultBean<Order>.Send(order);
        }

    }
}
