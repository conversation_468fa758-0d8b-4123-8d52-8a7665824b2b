﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.Domain
{
    [SugarTable("t_order_item")]
    public class OrderItem
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }
        public Guid OrderId { get; set; }

        public int MatchId { get; set; }

        public decimal Stake { get; set; }

        public decimal MaxPayout { get; set; }
        public decimal ActualPayout { get; set; }

        public decimal Odds { get; set; }

        public int OptionId { get; set; }

        public string? OptionName { get; set; }
        public Guid RoundId { get; set; }

        public string? OptionType { get; set; }

        public EOrderState Status { get; set; }

        public DateTime? CancelTime { get; set; }


        /// <summary>
        /// Desc:删除时间
        /// Default:
        /// Nullable:True
        /// </summary>    
        [JsonIgnore]
        public DateTime? DeleteTime { get; set; }

        /// <summary>
        /// Desc:创建人
        /// Default:
        /// Nullable:True
        /// </summary>
        [JsonIgnore]
        public string? CreateBy { get; set; }


        /// <summary>
        /// Desc:创建时间
        /// Default:
        /// Nullable:True
        /// </summary>     
        [SugarColumn(InsertServerTime = true)]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// Desc:更新时间
        /// Default:
        /// Nullable:True
        /// </summary>     
        [JsonIgnore]
        [SugarColumn(UpdateServerTime = true)]
        public DateTime? UpdateTime { get; set; }

    }
}
