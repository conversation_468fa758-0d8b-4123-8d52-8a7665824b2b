﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YStudio.Match.ColorLucky
{
    public class OrderItem
    {
        public int Id { get; set; }

        /// <summary>
        /// 订单类型 
        /// 0:数字组合
        /// 1:颜色
        /// </summary>
        public int Type { get; set; } = 0;

        /// <summary>
        /// 订单颜色
        /// </summary>
        public EColor? Color { get; set; }

        /// <summary>
        /// 订单组合数字
        /// </summary>
        public List<int> Numbers { get; set; }

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal Stake { get; set; } = 0;
        /// <summary>
        /// 赔付金额
        /// </summary>
        public decimal Payout { get; set; } = 0;


        ////包含该数字下的所有组合
        //public IList<OrderItem> Combinations { get; set; } = new List<OrderItem>();

        public OrderItem(List<int> numbers, decimal stake)
        {
            Numbers = numbers.OrderBy(x => x).ToList();
            Stake = stake;
            Type = 0;
        }
        public OrderItem(EColor color, decimal stake)
        {
            Color = color;
            Stake = stake;
            Type = 1;
        }
        public OrderItem()
        {
        }
    }
}
