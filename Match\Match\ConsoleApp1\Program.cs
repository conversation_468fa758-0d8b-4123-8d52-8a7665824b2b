﻿using ESCPOS_NET;
using ESCPOS_NET.Emitters;
using ESCPOS_NET.Utilities;
using System.Diagnostics;
using YStudio.Match.CarsRacing;
using YStudio.Match.ColorLucky;
using YStudio.Match.SpinWin;

namespace ConsoleApp1
{
    internal class Program
    {
        static void StatusChanged(object sender, EventArgs ps)
        {
            var status = (PrinterStatusEventArgs)ps;
            Console.WriteLine($"Status: {status.IsPrinterOnline}");
            Console.WriteLine($"Has Paper? {status.IsPaperOut}");
            Console.WriteLine($"Paper Running Low? {status.IsPaperLow}");
            Console.WriteLine($"Cash Drawer Open? {status.IsCashDrawerOpen}");
            Console.WriteLine($"Cover Open? {status.IsCoverOpen}");
        }

        static void Main(string[] args)
        {
            CarsRacingHandler carsRacingHandler = new CarsRacingHandler();
            var map = new Dictionary<int, int>();
            for (int i = 0; i < 1000; i++)
            {
                var numbers = carsRacingHandler.GetRandomNumbersByWeight(2);
                foreach (var number in numbers)
                {
                    if (map.ContainsKey(number))
                    {
                        map[number]++;
                    }
                    else
                    {
                        map[number] = 1;
                    }
                }

                //Console.WriteLine(string.Join(",", numbers));
            }

            //排序 map
            var sortedMap = map.OrderBy(kvp => kvp.Key).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            foreach (var kvp in sortedMap)
            {
                Console.WriteLine($"Number: {kvp.Key}, Count: {kvp.Value}");
            }

        }

            static void Main1(string[] args)
        {

            //var printer = new SerialPrinter(portName: "COM3", baudRate: 115200);
            //printer.StatusChanged += StatusChanged;
            //var e = new EPSON();
            //printer.Write(
            //      ByteSplicer.Combine(
            //        e.CenterAlign(),
            //        e.PrintLine(""),
            //        e.SetBarcodeHeightInDots(360),
            //        e.SetBarWidth(BarWidth.Default),
            //        e.SetBarLabelPosition(BarLabelPrintPosition.None),
            //        e.PrintBarcode(BarcodeType.ITF, "0123456789"),
            //        e.PrintLine(""),
            //        e.PrintLine("B&H PHOTO & VIDEO"),
            //        e.PrintLine("420 NINTH AVE."),
            //        e.PrintLine("NEW YORK, NY 10001"),
            //        e.PrintLine("(************* - (800)947-9975"),
            //        e.SetStyles(PrintStyle.Underline),
            //        e.PrintLine("www.bhphotovideo.com"),
            //        e.SetStyles(PrintStyle.None),
            //        e.PrintLine(""),
            //        e.LeftAlign(),
            //        e.PrintLine("Order: 123456789        Date: 02/01/19"),
            //        e.PrintLine(""),
            //        e.PrintLine(""),
            //        e.SetStyles(PrintStyle.FontB),
            //        e.PrintLine("1   TRITON LOW-NOISE IN-LINE MICROPHONE PREAMP"),
            //        e.PrintLine("    TRFETHEAD/FETHEAD                        89.95         89.95"),
            //        e.PrintLine("----------------------------------------------------------------"),
            //        e.RightAlign(),
            //        e.PrintLine("SUBTOTAL         89.95"),
            //        e.PrintLine("Total Order:         89.95"),
            //        e.PrintLine("Total Payment:         89.95"),
            //        e.PrintLine(""),
            //        e.LeftAlign(),
            //        e.SetStyles(PrintStyle.Bold | PrintStyle.FontB),
            //        e.PrintLine("SOLD TO:                        SHIP TO:"),
            //        e.SetStyles(PrintStyle.FontB),
            //        e.PrintLine("  FIRSTN LASTNAME                 FIRSTN LASTNAME"),
            //        e.PrintLine("  123 FAKE ST.                    123 FAKE ST."),
            //        e.PrintLine("  DECATUR, IL 12345               DECATUR, IL 12345"),
            //        e.PrintLine("  (123)456-7890                   (123)456-7890"),
            //        e.PrintLine("  CUST: 87654321"),
            //        e.PrintLine(""),
            //        e.PrintLine("")
            //      )

//);
            


            /*
            ColorLuckyHandler colorLuckyHandler = new ColorLuckyHandler();

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
         
            int i = 0;
            while (i < 1000)
            {
                var numbers = colorLuckyHandler.Handle(null, null);
                i++;
                Console.WriteLine(string.Join(",", numbers.Select(x=>x.Number)));
                Console.WriteLine($"处理时间: {stopwatch.ElapsedMilliseconds} 毫秒");
                stopwatch.Restart();
            }
            stopwatch.Stop();
            Console.WriteLine($"处理时间: {stopwatch.ElapsedMilliseconds} 毫秒");
            //CarsRacingHandler handler = new CarsRacingHandler();



            //ColorKenoHandler handler = new ColorKenoHandler();


            //List<int> numbers = new List<int> { 1, 2, 3, 4, 5, 6, 7, 8 };

            //for (int length = 3; length <= 8; length++)
            //{
            //    Console.WriteLine($"\n=== {length} 位数组合 ===");
            //    foreach (var combo in ColorKenoHandler.GetCombinations(numbers, length))
            //    {
            //        Console.WriteLine(string.Join("", combo));
            //    }
            //}

            //SpinWinHandler spinWinHandler = new SpinWinHandler();
            //int i = 0;
            //while (i < 10)
            //{
            //    var number = spinWinHandler.Handle(null);
            //    Console.WriteLine(number.Number);
            //    i++;
            //}

            */
        }
    }
}
