﻿
using FreeScheduler;
using YStudio.Infrastructure.WebSocket;

namespace YStudio.Match.Branch.Host.Services
{
    public class SchedulerHostedService : IHostedService, IDisposable
    {
        private readonly Scheduler _scheduler;

        public SchedulerHostedService(Scheduler scheduler)
        {
            _scheduler = scheduler;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {

            //生成每日游戏回合的记录
            var task = _scheduler.FindTask(t => t.Topic.Equals(EScheduleTopic.GenerateRoundTask));

            if (task.Length > 0)
            {
                return Task.CompletedTask;
            }

            //临时测试任务
            //_scheduler.AddTask(ScheduleTopic.MatchRoundWinnerByDayExecTime, "1001_020250506_45_fa5ba039-6ddb-491a-9c21-478b56419202", round: 1, 30);
            
            //启动游戏期数生成任务
            _scheduler.AddTask(EScheduleTopic.GenerateRoundTask, "GenerateRoundTask", round: -1, 60 * 5);
            //每5分钟触发，定期清理1小时之前的数据（单位：秒）
            _scheduler.AddTask("[系统预留]清理任务数据", "3600", round: -1, 60 * 5);
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _scheduler.Dispose();
        }
    }
}
