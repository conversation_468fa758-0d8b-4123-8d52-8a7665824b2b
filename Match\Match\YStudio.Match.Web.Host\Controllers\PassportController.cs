﻿using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using YStudio.Infrastructure;
using YStudio.Infrastructure.Authorization;
using YStudio.Match.CoreService;
using YStudio.Match.Domain;
using YStudio.Match.Repository;
using YStudio.Match.Web.Host.Dto;

namespace YStudio.Match.Web.Host.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PassportController : ControllerBase
    {
        [Autowired]
        private readonly AccountService accountService;

        [Autowired]
        private readonly DbContext dbContext;

        [Autowired]
        private readonly ICurrentUserService currentUserService;

        [HttpPost]
        public async Task<ResultBean<Account>> ChangePassword([FromBody] ChangePasswordDto dto)
        {

            Account account = await dbContext.Accounts.AsQueryable()
                .Where(x => x.Id == currentUserService.UserId)
                .FirstAsync();

            Throws.IfTrue(dto.NewPassword != dto.ConfirmPassword, "两次密码比对不正确");
            Throws.IfNullOrEmpty(account, "账户不存在");
            Throws.IfTrue(account.LoginPassword != EncodeUtils.ToMd5(dto.OriginalPassword), "账户原密码不正确");
            account.LoginPassword = EncodeUtils.ToMd5(dto.NewPassword);
            var res = await dbContext.Accounts.AsUpdateable(account)
                .UpdateColumns(a => new { a.LoginPassword })
                .ExecuteCommandAsync();

            return ResultBean<Account>.Success(account);
        }

        /// <summary>
        /// 账号登录
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ResultBean<AccountDto>> Login([FromBody] LoginDto dto)
        {
            Account account = await accountService.LoginAsync(dto.LoginAccount, dto.LoginPassword, dto.channel);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, account.Id.ToString()),
                new Claim(ClaimTypes.Name, account.NickName),
                new Claim(ClaimTypes.Role, account.AccountType.ToString()),
                new Claim("BranchId", account.BranchId==null ? "0" : account.BranchId.ToString()),
                new Claim("BranchName", account.Branch==null ? "" : account.Branch.BranchName)
            };

            var settings = InternalApp.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
            DateTime expireAt = DateTime.Now.AddSeconds(settings.ExpireSeconds);

            var token = JwtHelper.buildToken(claims, expireAt);

            var accountDto = account.Adapt<AccountDto>();
            accountDto.BranchName = account.Branch?.BranchName;
            accountDto.Token = token;
            accountDto.ExpireAt = expireAt;
            accountDto.accountType = account.AccountType.ToString();

            return ResultBean<AccountDto>.Success(accountDto);
        }
    }
}
